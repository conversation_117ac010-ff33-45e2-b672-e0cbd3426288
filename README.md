# NER数据生成器

一个用于生成中文命名实体识别(NER)训练数据的工具，支持字符级别的BIO标注格式。

## 功能特点

- **字符级别标注**: 生成字符级别的BIO标注数据，适合中文NER任务
- **多种实体类型**: 支持人名(PER)、组织机构(ORG)、地点(LOC)、时间(TIME)、金额(MONEY)、产品(PRODUCT)、事件(EVENT)等7种实体类型
- **复杂句子生成**: 支持简单和复杂两种句子模式，生成更真实的训练数据
- **多种输出格式**: 支持JSON、CoNLL、BIO三种输出格式
- **可配置**: 通过配置文件灵活调整生成参数
- **数据质量保证**: 内置BIO格式验证和数据统计分析

## 文件结构

```
├── ner_data_generator.py      # 基础版本生成器
├── enhanced_ner_generator.py  # 增强版生成器(推荐使用)
├── config.py                  # 配置文件
├── example_usage.py           # 使用示例
└── README.md                  # 说明文档
```

## 快速开始

### 1. 基本使用

```python
from enhanced_ner_generator import EnhancedNERGenerator

# 创建生成器
generator = EnhancedNERGenerator()

# 生成20000条数据
dataset = generator.generate_dataset(20000)

# 分析数据集
generator.analyze_dataset(dataset)

# 保存数据集
generator.save_dataset(dataset, ['json', 'conll', 'bio'])
```

### 2. 运行示例

```bash
# 运行基础版本
python ner_data_generator.py

# 运行增强版本
python enhanced_ner_generator.py

# 运行交互式示例
python example_usage.py
```

### 3. 自定义配置

```python
from enhanced_ner_generator import EnhancedNERGenerator

# 自定义配置
custom_config = {
    'num_samples': 10000,           # 生成样本数量
    'complex_sentence_ratio': 0.8,  # 复杂句子比例
    'output_formats': ['json'],     # 输出格式
    'random_seed': 42              # 随机种子
}

generator = EnhancedNERGenerator(custom_config)
dataset = generator.generate_dataset()
```

## 实体类型说明

| 实体类型 | 标签 | 说明 | 示例 |
|---------|------|------|------|
| 人名 | PER | 人物姓名 | 张三、李四 |
| 组织机构 | ORG | 公司、学校、政府机构等 | 北京大学、阿里巴巴 |
| 地点 | LOC | 地理位置、地名 | 北京市、中关村 |
| 时间 | TIME | 时间表达 | 2024年1月1日、今年3月 |
| 金额 | MONEY | 货币金额 | 100万元、50亿美元 |
| 产品 | PRODUCT | 产品名称 | iPhone 15、ChatGPT |
| 事件 | EVENT | 事件名称 | 春节联欢晚会、世界杯 |

## 输出格式

### 1. JSON格式
```json
{
  "id": 1,
  "sentence": "张三在北京大学工作",
  "chars": ["张", "三", "在", "北", "京", "大", "学", "工", "作"],
  "tags": ["B-PER", "I-PER", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O"],
  "length": 9,
  "complexity": "simple"
}
```

### 2. CoNLL格式
```
# Sentence 1: 张三在北京大学工作
张	B-PER
三	I-PER
在	O
北	B-ORG
京	I-ORG
大	I-ORG
学	I-ORG
工	O
作	O

```

### 3. BIO格式
```
张 三 在 北 京 大 学 工 作
B-PER I-PER O B-ORG I-ORG I-ORG I-ORG O O

```

## 配置参数

在`config.py`中可以调整以下参数：

- `num_samples`: 生成样本数量(默认20000)
- `complex_sentence_ratio`: 复杂句子比例(默认0.7)
- `output_formats`: 输出格式列表(默认['json', 'conll', 'bio'])
- `random_seed`: 随机种子(默认42)

## 数据质量

生成的数据具有以下特点：

1. **真实性**: 使用真实的中文人名、地名、机构名等
2. **多样性**: 支持简单和复杂两种句子结构
3. **准确性**: 内置BIO格式验证，确保标注正确性
4. **平衡性**: 各种实体类型分布相对均衡

## 使用建议

1. **训练数据**: 建议生成10000-50000条数据用于模型训练
2. **验证数据**: 可以设置不同的随机种子生成验证集
3. **数据预处理**: 根据具体模型需求调整输出格式
4. **质量检查**: 使用内置的验证功能检查数据质量

## 扩展功能

可以通过修改`config.py`来扩展：

1. 添加新的实体类型
2. 增加词汇库
3. 自定义句子模板
4. 调整时间和金额格式

## 注意事项

1. 生成大量数据时请确保有足够的磁盘空间
2. 建议在生成前先用小样本测试配置
3. 生成的数据仅用于模型训练，不代表真实信息
4. 可以根据具体任务需求调整实体类型和句子复杂度

## 许可证

MIT License
