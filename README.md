# NER数据生成器

一个用于生成中文命名实体识别(NER)训练数据的工具，支持字符级别的BIO标注格式。

## 项目结构

```
├── src/                    # 核心源码
│   ├── ner_generator.py   # NER数据生成器核心类
│   └── config.py          # 配置文件
├── utils/                 # 工具模块
│   └── validator.py       # 数据验证工具
├── examples/              # 使用示例
│   └── basic_usage.py     # 基本使用示例
├── data/                  # 数据文件目录
├── docs/                  # 文档目录
├── main.py               # 主程序入口
└── README.md             # 项目说明
```

## 快速开始

### 1. 生成完整数据集（推荐）

```bash
python main.py
```

这将生成20,000条NER训练数据，包含JSON、CoNLL、BIO三种格式。

### 2. 自定义生成

```python
from src.ner_generator import NERGenerator

# 创建生成器
generator = NERGenerator()

# 生成数据
dataset = generator.generate_dataset(10000)  # 生成10000条

# 保存数据
generator.save_dataset(dataset, formats=['json', 'bio'])
```

### 3. 运行示例

```bash
cd examples
python basic_usage.py
```

## 功能特点

- **字符级别标注**: 适合中文NER任务
- **7种实体类型**: PER、ORG、LOC、TIME、MONEY、PRODUCT、EVENT
- **多种输出格式**: JSON、CoNLL、BIO
- **数据质量保证**: 内置验证和统计分析
- **高度可配置**: 灵活的配置选项

## 实体类型

| 类型 | 说明 | 示例 |
|------|------|------|
| PER | 人名 | 张三、李四 |
| ORG | 组织机构 | 北京大学、阿里巴巴 |
| LOC | 地点 | 北京市、中关村 |
| TIME | 时间 | 2024年1月1日 |
| MONEY | 金额 | 100万元 |
| PRODUCT | 产品 | iPhone 15 |
| EVENT | 事件 | 春节联欢晚会 |

## 输出格式

### JSON格式
```json
{
  "id": 1,
  "sentence": "张三在北京大学工作",
  "chars": ["张", "三", "在", "北", "京", "大", "学", "工", "作"],
  "tags": ["B-PER", "I-PER", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O"]
}
```

### BIO格式
```
张 三 在 北 京 大 学 工 作
B-PER I-PER O B-ORG I-ORG I-ORG I-ORG O O
```

## 配置选项

在 `src/config.py` 中可以调整：

- `num_samples`: 生成样本数量
- `complex_sentence_ratio`: 复杂句子比例
- `output_formats`: 输出格式
- `random_seed`: 随机种子

## 数据验证

使用内置验证工具检查数据质量：

```python
from utils.validator import DataValidator

DataValidator.validate_dataset("data/ner_dataset_xxx.json")
```

## 许可证

MIT License
