#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查新生成的数据质量
"""

import json
import random
from collections import Counter

def load_latest_dataset():
    """加载最新的数据集"""
    import glob
    json_files = glob.glob("data/ner_dataset_*.json")
    if not json_files:
        print("未找到数据文件！")
        return None
    
    latest_file = max(json_files)
    print(f"检查数据集: {latest_file}")
    
    with open(latest_file, 'r', encoding='utf-8') as f:
        return json.load(f)

def check_specific_issues(dataset):
    """检查特定的标注问题"""
    print("\n=== 检查特定标注问题 ===")
    
    issues = []
    
    # 检查"于"是否还被错误标注为PER
    for sample in dataset:
        sentence = sample['sentence']
        chars = sample['chars']
        tags = sample['tags']
        
        for i, (char, tag) in enumerate(zip(chars, tags)):
            if char == '于' and tag.startswith('B-PER'):
                issues.append(f"样本{sample['id']}: '于'被错误标注为{tag} - {sentence}")
            elif char == '于' and tag.startswith('I-PER'):
                issues.append(f"样本{sample['id']}: '于'被错误标注为{tag} - {sentence}")
    
    if issues:
        print(f"发现{len(issues)}个'于'标注错误:")
        for issue in issues[:5]:  # 只显示前5个
            print(f"  {issue}")
    else:
        print("✓ '于'的标注问题已修复")
    
    return len(issues) == 0

def show_sample_annotations(dataset, num_samples=10):
    """展示随机样本的标注情况"""
    print(f"\n=== 随机样本标注展示 ({num_samples}个) ===")
    
    samples = random.sample(dataset, min(num_samples, len(dataset)))
    
    for i, sample in enumerate(samples, 1):
        print(f"\n样本 {i} (ID: {sample['id']}):")
        print(f"句子: {sample['sentence']}")
        
        # 提取实体
        entities = []
        current_entity = ""
        current_type = ""
        
        for char, tag in zip(sample['chars'], sample['tags']):
            if tag.startswith('B-'):
                if current_entity:
                    entities.append((current_entity, current_type))
                current_entity = char
                current_type = tag[2:]
            elif tag.startswith('I-') and current_entity:
                current_entity += char
            else:
                if current_entity:
                    entities.append((current_entity, current_type))
                    current_entity = ""
                    current_type = ""
        
        if current_entity:
            entities.append((current_entity, current_type))
        
        if entities:
            print("识别的实体:")
            for entity, entity_type in entities:
                print(f"  {entity} -> {entity_type}")
        else:
            print("未识别到实体")

def check_person_name_quality(dataset):
    """检查人名标注质量"""
    print("\n=== 人名标注质量检查 ===")
    
    person_entities = []
    
    for sample in dataset:
        chars = sample['chars']
        tags = sample['tags']
        
        current_entity = ""
        
        for char, tag in zip(chars, tags):
            if tag.startswith('B-PER'):
                if current_entity:
                    person_entities.append(current_entity)
                current_entity = char
            elif tag.startswith('I-PER') and current_entity:
                current_entity += char
            else:
                if current_entity:
                    person_entities.append(current_entity)
                    current_entity = ""
        
        if current_entity:
            person_entities.append(current_entity)
    
    # 统计人名
    person_counter = Counter(person_entities)
    
    print(f"识别到的人名总数: {len(person_entities)}")
    print(f"唯一人名数量: {len(person_counter)}")
    
    print("\n最常见的人名:")
    for name, count in person_counter.most_common(10):
        print(f"  {name}: {count} 次")
    
    # 检查可疑的人名（单字符或包含停用词）
    suspicious_names = []
    stop_chars = ['于', '在', '和', '与', '及', '或', '但', '而', '的', '了', '是', '有', '为', '将', '被', '把']
    
    for name in person_counter.keys():
        if len(name) == 1:
            suspicious_names.append(f"单字符人名: {name}")
        elif any(stop_char in name for stop_char in stop_chars):
            suspicious_names.append(f"包含停用词: {name}")
    
    if suspicious_names:
        print(f"\n发现{len(suspicious_names)}个可疑人名:")
        for name in suspicious_names[:10]:
            print(f"  {name}")
    else:
        print("\n✓ 人名标注质量良好")

def check_bio_consistency(dataset):
    """检查BIO标注一致性"""
    print("\n=== BIO标注一致性检查 ===")
    
    errors = []
    
    for sample in dataset:
        tags = sample['tags']
        
        for i, tag in enumerate(tags):
            if tag.startswith('I-'):
                if i == 0:
                    errors.append(f"样本{sample['id']}: I标签出现在句子开头")
                else:
                    prev_tag = tags[i-1]
                    entity_type = tag[2:]
                    if not (prev_tag == f'B-{entity_type}' or prev_tag == f'I-{entity_type}'):
                        errors.append(f"样本{sample['id']}: I标签前不是对应的B或I标签")
    
    if errors:
        print(f"发现{len(errors)}个BIO一致性错误:")
        for error in errors[:5]:
            print(f"  {error}")
    else:
        print("✓ BIO标注一致性良好")

def main():
    """主检查函数"""
    print("NER数据质量检查")
    print("=" * 50)
    
    # 加载数据
    dataset = load_latest_dataset()
    if not dataset:
        return
    
    print(f"数据集大小: {len(dataset)} 条")
    
    # 执行各项检查
    check_specific_issues(dataset)
    check_bio_consistency(dataset)
    check_person_name_quality(dataset)
    show_sample_annotations(dataset, 8)
    
    print("\n" + "=" * 50)
    print("数据质量检查完成！")

if __name__ == "__main__":
    main()
