#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NER数据全面验证脚本 - 确保数据绝对准确性
包含可疑错误检测功能，支持人工修改
"""

import json
import re
from typing import List, Dict, Tuple, Set
from collections import Counter, defaultdict
import time

class ComprehensiveValidator:
    """全面数据验证器"""
    
    def __init__(self):
        # 定义已知词汇库用于验证
        self.known_surnames = {'张', '李', '王', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林'}
        self.known_given_names = {'伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '娟', '涛', '明'}
        
        self.known_organizations = {
            '北京大学', '清华大学', '阿里巴巴集团', '腾讯科技', '百度公司', '华为技术有限公司',
            '中国移动', '工商银行', '建设银行', '中央电视台', '新华社'
        }
        
        self.known_locations = {
            '北京市', '上海市', '广州市', '深圳市', '杭州市', '南京市', '武汉市', '成都市',
            '中关村', '陆家嘴', '珠江新城', '海淀区', '朝阳区'
        }
        
        self.known_products = {
            'iPhone 15', 'MacBook Pro', '小米手机', '华为Mate60', '微信', '支付宝', '抖音', '淘宝'
        }
        
        self.known_events = {
            '春节联欢晚会', '世界杯', '奥运会', '进博会', '双十一购物节', '开发者大会', '新品发布会'
        }
        
        # 停用词和常见介词
        self.stop_words = {
            '于', '在', '和', '与', '及', '或', '但', '而', '的', '了', '是', '有', '为', '将', '被', '把',
            '从', '到', '向', '往', '由', '对', '给', '让', '使', '令', '叫', '请', '要', '想', '能', '会',
            '可', '应', '该', '须', '必', '得', '需', '用', '以', '所', '其', '此', '这', '那', '哪', '什',
            '么', '怎', '为什么', '因为', '所以', '如果', '虽然', '但是', '然而', '不过', '只是', '就是'
        }
        
        # 时间和金额正则模式
        self.time_patterns = [
            r'\d{4}年\d{1,2}月\d{1,2}日',
            r'\d{1,2}月\d{1,2}日',
            r'今年\d{1,2}月',
            r'明年\d{1,2}月',
            r'去年\d{1,2}月',
            r'\d{4}年',
            r'上周[一二三四五六日]',
            r'下个月\d{1,2}号',
            r'本月\d{1,2}号'
        ]
        
        self.money_patterns = [
            r'\d+万元',
            r'\d+亿元',
            r'\d+元',
            r'\d+\.\d+万元',
            r'\d+万美元',
            r'\d+亿美元',
            r'\d+万人民币',
            r'\d+万港币'
        ]
        
        # 错误统计
        self.errors = []
        self.suspicious_cases = []
        self.warnings = []
    
    def load_dataset(self, filename: str) -> List[Dict]:
        """加载数据集"""
        print(f"加载数据集: {filename}")
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def validate_bio_format(self, dataset: List[Dict]) -> bool:
        """严格验证BIO格式"""
        print("\n=== BIO格式严格验证 ===")
        format_errors = []
        
        for sample in dataset:
            sample_id = sample['id']
            chars = sample['chars']
            tags = sample['tags']
            
            if len(chars) != len(tags):
                format_errors.append(f"样本{sample_id}: 字符数({len(chars)})与标签数({len(tags)})不匹配")
                continue
            
            for i, tag in enumerate(tags):
                # 检查标签格式
                if tag not in ['O'] and not re.match(r'^[BI]-[A-Z]+$', tag):
                    format_errors.append(f"样本{sample_id}: 无效标签格式 '{tag}' 在位置{i}")
                
                # 检查I标签的前置条件
                if tag.startswith('I-'):
                    if i == 0:
                        format_errors.append(f"样本{sample_id}: I标签不能出现在句子开头 (位置{i})")
                    else:
                        prev_tag = tags[i-1]
                        entity_type = tag[2:]
                        expected_tags = [f'B-{entity_type}', f'I-{entity_type}']
                        if prev_tag not in expected_tags:
                            format_errors.append(f"样本{sample_id}: I-{entity_type}标签前应为B-{entity_type}或I-{entity_type}，实际为'{prev_tag}' (位置{i})")
        
        if format_errors:
            print(f"❌ 发现{len(format_errors)}个BIO格式错误:")
            for error in format_errors[:10]:
                print(f"  {error}")
            self.errors.extend(format_errors)
            return False
        else:
            print("✅ BIO格式验证通过")
            return True
    
    def validate_entity_accuracy(self, dataset: List[Dict]) -> bool:
        """验证实体标注准确性"""
        print("\n=== 实体标注准确性验证 ===")
        accuracy_errors = []
        
        for sample in dataset:
            sample_id = sample['id']
            sentence = sample['sentence']
            chars = sample['chars']
            tags = sample['tags']
            
            # 提取标注的实体
            entities = self._extract_entities(chars, tags)
            
            # 验证每个实体
            for entity_text, entity_type, start_pos, end_pos in entities:
                error_msg = self._validate_single_entity(entity_text, entity_type, sentence, sample_id, start_pos)
                if error_msg:
                    accuracy_errors.append(error_msg)
        
        if accuracy_errors:
            print(f"❌ 发现{len(accuracy_errors)}个实体准确性错误:")
            for error in accuracy_errors[:15]:
                print(f"  {error}")
            self.errors.extend(accuracy_errors)
            return False
        else:
            print("✅ 实体标注准确性验证通过")
            return True

    def _extract_entities(self, chars: List[str], tags: List[str]) -> List[Tuple[str, str, int, int]]:
        """提取实体信息"""
        entities = []
        current_entity = ""
        current_type = ""
        start_pos = -1

        for i, (char, tag) in enumerate(zip(chars, tags)):
            if tag.startswith('B-'):
                if current_entity:
                    entities.append((current_entity, current_type, start_pos, i-1))
                current_entity = char
                current_type = tag[2:]
                start_pos = i
            elif tag.startswith('I-') and current_entity:
                current_entity += char
            else:
                if current_entity:
                    entities.append((current_entity, current_type, start_pos, i-1))
                    current_entity = ""
                    current_type = ""
                    start_pos = -1

        if current_entity:
            entities.append((current_entity, current_type, start_pos, len(chars)-1))

        return entities

    def _validate_single_entity(self, entity_text: str, entity_type: str, sentence: str, sample_id: int, start_pos: int) -> str:
        """验证单个实体的准确性"""
        # 验证人名
        if entity_type == 'PER':
            return self._validate_person_name(entity_text, sentence, sample_id, start_pos)

        # 验证组织
        elif entity_type == 'ORG':
            if entity_text not in self.known_organizations:
                return f"样本{sample_id}: 未知组织 '{entity_text}'"

        # 验证地点
        elif entity_type == 'LOC':
            if entity_text not in self.known_locations:
                return f"样本{sample_id}: 未知地点 '{entity_text}'"

        # 验证产品
        elif entity_type == 'PRODUCT':
            if entity_text not in self.known_products:
                return f"样本{sample_id}: 未知产品 '{entity_text}'"

        # 验证事件
        elif entity_type == 'EVENT':
            if entity_text not in self.known_events:
                return f"样本{sample_id}: 未知事件 '{entity_text}'"

        # 验证时间
        elif entity_type == 'TIME':
            if not self._is_valid_time(entity_text):
                return f"样本{sample_id}: 无效时间格式 '{entity_text}'"

        # 验证金额
        elif entity_type == 'MONEY':
            if not self._is_valid_money(entity_text):
                return f"样本{sample_id}: 无效金额格式 '{entity_text}'"

        return None

    def _validate_person_name(self, name: str, sentence: str, sample_id: int, start_pos: int) -> str:
        """验证人名的准确性"""
        # 检查长度
        if len(name) < 2:
            return f"样本{sample_id}: 人名过短 '{name}'"

        if len(name) > 4:
            return f"样本{sample_id}: 人名过长 '{name}'"

        # 检查是否包含停用词
        for stop_word in self.stop_words:
            if stop_word in name:
                return f"样本{sample_id}: 人名包含停用词 '{name}' (包含'{stop_word}')"

        # 检查姓氏
        if name[0] not in self.known_surnames:
            return f"样本{sample_id}: 未知姓氏 '{name}' (姓氏'{name[0]}')"

        # 检查名字部分
        for char in name[1:]:
            if char not in self.known_given_names:
                return f"样本{sample_id}: 未知名字字符 '{name}' (字符'{char}')"

        # 检查上下文合理性
        context_error = self._check_person_context(name, sentence, start_pos, sample_id)
        if context_error:
            return context_error

        return None

    def _check_person_context(self, name: str, sentence: str, start_pos: int, sample_id: int) -> str:
        """检查人名的上下文合理性"""
        # 检查前后字符
        if start_pos > 0:
            prev_char = sentence[start_pos - 1]
            if prev_char in self.stop_words:
                return f"样本{sample_id}: 人名前有停用词 '{name}' (前字符'{prev_char}')"

        end_pos = start_pos + len(name)
        if end_pos < len(sentence):
            next_char = sentence[end_pos]
            if next_char in self.stop_words:
                # 这是可疑情况，不是错误
                self.suspicious_cases.append(f"样本{sample_id}: 人名后有停用词 '{name}' (后字符'{next_char}') - 句子: {sentence}")

        return None

    def _is_valid_time(self, time_text: str) -> bool:
        """验证时间格式"""
        for pattern in self.time_patterns:
            if re.fullmatch(pattern, time_text):
                return True
        return False

    def _is_valid_money(self, money_text: str) -> bool:
        """验证金额格式"""
        for pattern in self.money_patterns:
            if re.fullmatch(pattern, money_text):
                return True
        return False

    def detect_suspicious_patterns(self, dataset: List[Dict]) -> bool:
        """检测可疑模式"""
        print("\n=== 可疑模式检测 ===")

        # 统计各种模式
        entity_stats = defaultdict(Counter)
        context_patterns = defaultdict(list)

        for sample in dataset:
            sample_id = sample['id']
            sentence = sample['sentence']
            chars = sample['chars']
            tags = sample['tags']

            entities = self._extract_entities(chars, tags)

            for entity_text, entity_type, start_pos, end_pos in entities:
                entity_stats[entity_type][entity_text] += 1

                # 收集上下文
                context_before = sentence[max(0, start_pos-3):start_pos] if start_pos > 0 else ""
                context_after = sentence[end_pos+1:min(len(sentence), end_pos+4)] if end_pos < len(sentence)-1 else ""
                context_patterns[entity_type].append((entity_text, context_before, context_after, sample_id, sentence))

        # 检测异常频率
        self._detect_frequency_anomalies(entity_stats)

        # 检测上下文异常
        self._detect_context_anomalies(context_patterns)

        # 检测单字符实体
        self._detect_single_char_entities(entity_stats)

        # 检测重复模式
        self._detect_repetitive_patterns(dataset)

        print(f"检测到 {len(self.suspicious_cases)} 个可疑情况")
        return len(self.suspicious_cases) == 0

    def _detect_frequency_anomalies(self, entity_stats: Dict):
        """检测频率异常"""
        for entity_type, counter in entity_stats.items():
            total_count = sum(counter.values())

            for entity, count in counter.items():
                frequency = count / total_count

                # 如果某个实体出现频率过高，标记为可疑
                if frequency > 0.1:  # 超过10%
                    self.suspicious_cases.append(f"高频实体: {entity_type} '{entity}' 出现{count}次 (频率{frequency:.2%})")

                # 如果某个实体只出现一次且看起来可疑
                if count == 1 and len(entity) == 1:
                    self.suspicious_cases.append(f"单次单字符实体: {entity_type} '{entity}'")

    def _detect_context_anomalies(self, context_patterns: Dict):
        """检测上下文异常"""
        for entity_type, patterns in context_patterns.items():
            for entity_text, before, after, sample_id, sentence in patterns:
                # 检查是否在停用词附近
                if before and any(stop in before for stop in ['用', '于', '在', '的']):
                    if entity_type == 'PER':
                        self.suspicious_cases.append(f"样本{sample_id}: 人名'{entity_text}'前有可疑上下文'{before}' - {sentence}")

                if after and any(stop in after for stop in ['用', '于', '在', '的']):
                    if entity_type == 'PER':
                        self.suspicious_cases.append(f"样本{sample_id}: 人名'{entity_text}'后有可疑上下文'{after}' - {sentence}")

    def _detect_single_char_entities(self, entity_stats: Dict):
        """检测单字符实体"""
        for entity_type, counter in entity_stats.items():
            for entity, count in counter.items():
                if len(entity) == 1:
                    if entity_type in ['PER', 'ORG', 'LOC']:  # 这些类型通常不应该是单字符
                        self.suspicious_cases.append(f"单字符{entity_type}实体: '{entity}' (出现{count}次)")

    def _detect_repetitive_patterns(self, dataset: List[Dict]):
        """检测重复模式"""
        sentence_patterns = Counter()

        for sample in dataset:
            # 简化句子模式（替换实体为占位符）
            pattern = self._get_sentence_pattern(sample)
            sentence_patterns[pattern] += 1

        for pattern, count in sentence_patterns.items():
            if count > 100:  # 如果同一模式出现超过100次
                self.suspicious_cases.append(f"高重复句子模式 (出现{count}次): {pattern}")

    def _get_sentence_pattern(self, sample: Dict) -> str:
        """获取句子模式"""
        chars = sample['chars']
        tags = sample['tags']

        pattern = []
        for char, tag in zip(chars, tags):
            if tag == 'O':
                pattern.append(char)
            else:
                entity_type = tag.split('-')[1]
                pattern.append(f'[{entity_type}]')

        return ''.join(pattern)

    def check_completeness(self, dataset: List[Dict]) -> bool:
        """检查标注完整性"""
        print("\n=== 标注完整性检查 ===")
        completeness_issues = []

        for sample in dataset:
            sample_id = sample['id']
            sentence = sample['sentence']
            chars = sample['chars']
            tags = sample['tags']

            # 检查是否有明显的实体被遗漏
            missing_entities = self._find_missing_entities(sentence, chars, tags, sample_id)
            completeness_issues.extend(missing_entities)

        if completeness_issues:
            print(f"⚠️ 发现{len(completeness_issues)}个可能的遗漏标注:")
            for issue in completeness_issues[:10]:
                print(f"  {issue}")
            self.warnings.extend(completeness_issues)
        else:
            print("✅ 标注完整性检查通过")

        return len(completeness_issues) == 0

    def _find_missing_entities(self, sentence: str, chars: List[str], tags: List[str], sample_id: int) -> List[str]:
        """查找可能遗漏的实体"""
        missing = []

        # 检查已知组织是否被遗漏
        for org in self.known_organizations:
            if org in sentence:
                # 检查是否已被标注
                if not self._is_entity_tagged(sentence, org, chars, tags, 'ORG'):
                    missing.append(f"样本{sample_id}: 可能遗漏组织 '{org}' - {sentence}")

        # 检查已知地点是否被遗漏
        for loc in self.known_locations:
            if loc in sentence:
                if not self._is_entity_tagged(sentence, loc, chars, tags, 'LOC'):
                    missing.append(f"样本{sample_id}: 可能遗漏地点 '{loc}' - {sentence}")

        # 检查已知产品是否被遗漏
        for product in self.known_products:
            if product in sentence:
                if not self._is_entity_tagged(sentence, product, chars, tags, 'PRODUCT'):
                    missing.append(f"样本{sample_id}: 可能遗漏产品 '{product}' - {sentence}")

        # 检查时间表达是否被遗漏
        for pattern in self.time_patterns:
            matches = re.finditer(pattern, sentence)
            for match in matches:
                time_text = match.group()
                if not self._is_entity_tagged(sentence, time_text, chars, tags, 'TIME'):
                    missing.append(f"样本{sample_id}: 可能遗漏时间 '{time_text}' - {sentence}")

        # 检查金额表达是否被遗漏
        for pattern in self.money_patterns:
            matches = re.finditer(pattern, sentence)
            for match in matches:
                money_text = match.group()
                if not self._is_entity_tagged(sentence, money_text, chars, tags, 'MONEY'):
                    missing.append(f"样本{sample_id}: 可能遗漏金额 '{money_text}' - {sentence}")

        return missing

    def _is_entity_tagged(self, sentence: str, entity_text: str, chars: List[str], tags: List[str], expected_type: str) -> bool:
        """检查实体是否已被正确标注"""
        sentence_from_chars = ''.join(chars)
        if sentence != sentence_from_chars:
            return True  # 如果字符重构的句子不匹配，跳过检查

        pos = sentence.find(entity_text)
        if pos == -1:
            return True  # 实体不在句子中

        # 检查对应位置的标签
        for i in range(len(entity_text)):
            if pos + i >= len(tags):
                return False

            tag = tags[pos + i]
            if i == 0:
                if tag != f'B-{expected_type}':
                    return False
            else:
                if tag != f'I-{expected_type}':
                    return False

        return True

    def generate_report(self, dataset: List[Dict], output_file: str = "validation_report.txt"):
        """生成详细的验证报告"""
        print(f"\n=== 生成验证报告: {output_file} ===")

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("NER数据集全面验证报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"验证时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"数据集大小: {len(dataset)} 条\n\n")

            # 错误统计
            f.write(f"严重错误数量: {len(self.errors)}\n")
            f.write(f"可疑情况数量: {len(self.suspicious_cases)}\n")
            f.write(f"警告数量: {len(self.warnings)}\n\n")

            # 详细错误列表
            if self.errors:
                f.write("严重错误列表:\n")
                f.write("-" * 30 + "\n")
                for i, error in enumerate(self.errors, 1):
                    f.write(f"{i}. {error}\n")
                f.write("\n")

            # 可疑情况列表
            if self.suspicious_cases:
                f.write("可疑情况列表 (需要人工检查):\n")
                f.write("-" * 30 + "\n")
                for i, case in enumerate(self.suspicious_cases, 1):
                    f.write(f"{i}. {case}\n")
                f.write("\n")

            # 警告列表
            if self.warnings:
                f.write("警告列表:\n")
                f.write("-" * 30 + "\n")
                for i, warning in enumerate(self.warnings, 1):
                    f.write(f"{i}. {warning}\n")
                f.write("\n")

            # 统计信息
            entity_stats = self._generate_statistics(dataset)
            f.write("数据集统计信息:\n")
            f.write("-" * 30 + "\n")
            for stat in entity_stats:
                f.write(f"{stat}\n")

        print(f"验证报告已保存到: {output_file}")

    def _generate_statistics(self, dataset: List[Dict]) -> List[str]:
        """生成统计信息"""
        stats = []

        # 实体类型统计
        entity_counts = Counter()
        total_entities = 0

        for sample in dataset:
            entities = self._extract_entities(sample['chars'], sample['tags'])
            for _, entity_type, _, _ in entities:
                entity_counts[entity_type] += 1
                total_entities += 1

        stats.append(f"总实体数: {total_entities}")
        for entity_type, count in entity_counts.most_common():
            percentage = count / total_entities * 100 if total_entities > 0 else 0
            stats.append(f"{entity_type}: {count} 个 ({percentage:.1f}%)")

        # 句子长度统计
        lengths = [sample['length'] for sample in dataset]
        stats.append(f"平均句子长度: {sum(lengths) / len(lengths):.2f} 字符")
        stats.append(f"最短句子: {min(lengths)} 字符")
        stats.append(f"最长句子: {max(lengths)} 字符")

        return stats

    def comprehensive_validate(self, filename: str) -> bool:
        """执行全面验证"""
        print("开始全面数据验证...")
        print("=" * 60)

        start_time = time.time()

        # 加载数据
        dataset = self.load_dataset(filename)

        # 清空之前的错误记录
        self.errors = []
        self.suspicious_cases = []
        self.warnings = []

        # 执行各项验证
        print("正在执行严格验证，请耐心等待...")

        bio_valid = self.validate_bio_format(dataset)
        accuracy_valid = self.validate_entity_accuracy(dataset)
        suspicious_clean = self.detect_suspicious_patterns(dataset)
        completeness_ok = self.check_completeness(dataset)

        # 生成报告
        self.generate_report(dataset)

        end_time = time.time()

        # 总结
        print(f"\n{'='*60}")
        print("验证完成总结:")
        print(f"验证耗时: {end_time - start_time:.2f} 秒")
        print(f"严重错误: {len(self.errors)} 个")
        print(f"可疑情况: {len(self.suspicious_cases)} 个")
        print(f"警告: {len(self.warnings)} 个")

        if self.errors:
            print("\n❌ 数据集存在严重错误，需要修复！")
            return False
        elif self.suspicious_cases:
            print("\n⚠️ 数据集存在可疑情况，建议人工检查")
            return self._handle_suspicious_cases()
        else:
            print("\n✅ 数据集验证通过，质量良好！")
            return True

    def _handle_suspicious_cases(self) -> bool:
        """处理可疑情况"""
        print(f"\n发现 {len(self.suspicious_cases)} 个可疑情况:")

        # 显示前10个可疑情况
        for i, case in enumerate(self.suspicious_cases[:10], 1):
            print(f"{i}. {case}")

        if len(self.suspicious_cases) > 10:
            print(f"... 还有 {len(self.suspicious_cases) - 10} 个可疑情况")

        print("\n建议:")
        print("1. 查看 validation_report.txt 获取完整的可疑情况列表")
        print("2. 人工检查这些可疑情况")
        print("3. 如果确认无误，可以继续使用数据集")
        print("4. 如果发现问题，请修复后重新验证")

        while True:
            choice = input("\n是否继续使用数据集? (y/n/详细查看可疑情况[d]): ").lower().strip()
            if choice == 'y':
                return True
            elif choice == 'n':
                return False
            elif choice == 'd':
                self._show_detailed_suspicious_cases()
            else:
                print("请输入 y、n 或 d")

    def _show_detailed_suspicious_cases(self):
        """显示详细的可疑情况"""
        print("\n详细可疑情况:")
        print("-" * 50)

        # 按类型分组显示
        case_types = defaultdict(list)
        for case in self.suspicious_cases:
            if "高频实体" in case:
                case_types["高频实体"].append(case)
            elif "单字符" in case:
                case_types["单字符实体"].append(case)
            elif "上下文" in case:
                case_types["上下文异常"].append(case)
            elif "重复模式" in case:
                case_types["重复模式"].append(case)
            else:
                case_types["其他"].append(case)

        for case_type, cases in case_types.items():
            print(f"\n{case_type} ({len(cases)}个):")
            for case in cases[:5]:  # 每类最多显示5个
                print(f"  - {case}")
            if len(cases) > 5:
                print(f"  ... 还有 {len(cases) - 5} 个")


def main():
    """主函数"""
    import glob

    # 查找最新的数据集文件
    json_files = glob.glob("data/ner_dataset_*.json")
    if not json_files:
        print("未找到数据集文件！")
        return

    latest_file = max(json_files)
    print(f"将验证数据集: {latest_file}")

    # 创建验证器
    validator = ComprehensiveValidator()

    # 执行全面验证
    is_valid = validator.comprehensive_validate(latest_file)

    if is_valid:
        print("\n🎉 数据集验证通过，可以安全用于模型训练！")
    else:
        print("\n⚠️ 数据集需要修复后才能使用")


if __name__ == "__main__":
    main()
