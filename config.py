#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NER数据生成器配置文件
"""

# 数据生成配置
DATASET_CONFIG = {
    'num_samples': 20000,  # 生成样本数量
    'complex_sentence_ratio': 0.7,  # 复杂句子比例
    'output_formats': ['json', 'conll', 'bio'],  # 输出格式
    'random_seed': 42  # 随机种子，用于复现结果
}

# 实体类型配置
ENTITY_TYPES = {
    'PER': '人名',
    'ORG': '组织机构', 
    'LOC': '地点',
    'TIME': '时间',
    'MONEY': '金额',
    'PRODUCT': '产品',
    'EVENT': '事件'
}

# 扩展词汇库
EXTENDED_VOCAB = {
    'surnames': [
        '张', '李', '王', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', 
        '胡', '朱', '高', '林', '何', '郭', '马', '罗', '梁', '宋', '郑', '谢', 
        '韩', '唐', '冯', '于', '董', '萧', '程', '曹', '袁', '邓', '许', '傅'
    ],
    
    'given_names': [
        '伟', '芳', '娜', '秀英', '敏', '静', '丽', '强', '磊', '军', '洋', '勇',
        '艳', '杰', '娟', '涛', '明', '超', '秀兰', '霞', '鹏', '辉', '刚', '健',
        '斌', '华', '建', '国', '庆', '春', '秋', '冬', '雪', '梅', '兰', '竹'
    ],
    
    'organizations': [
        '北京大学', '清华大学', '中国科学院', '阿里巴巴集团', '腾讯科技', '百度公司',
        '华为技术有限公司', '中国移动', '工商银行', '建设银行', '中国石油', '国家电网',
        '中央电视台', '人民日报社', '新华社', '中国银行', '农业银行', '招商银行',
        '字节跳动', '美团', '京东集团', '网易', '新浪', '搜狐', '小米科技', '滴滴出行',
        '中国科技大学', '复旦大学', '上海交通大学', '浙江大学', '南京大学', '中山大学'
    ],
    
    'locations': [
        '北京市', '上海市', '广州市', '深圳市', '杭州市', '南京市', '武汉市', '成都市',
        '西安市', '重庆市', '天津市', '苏州市', '长沙市', '郑州市', '青岛市', '大连市',
        '中关村', '陆家嘴', '珠江新城', '前海', '滨江区', '浦东新区', '海淀区', '朝阳区',
        '福州市', '厦门市', '合肥市', '济南市', '石家庄市', '太原市', '沈阳市', '长春市',
        '哈尔滨市', '昆明市', '贵阳市', '南宁市', '海口市', '银川市', '西宁市', '乌鲁木齐市'
    ],
    
    'products': [
        'iPhone 15', 'MacBook Pro', '小米手机', '华为Mate60', 'Tesla Model 3',
        '比亚迪汉', '理想ONE', '蔚来ES8', 'ChatGPT', '文心一言', '通义千问',
        '微信', '支付宝', '抖音', '快手', '淘宝', '京东', '美团', '滴滴出行',
        'iPad Pro', 'Surface Pro', '华为MateBook', '联想ThinkPad', 'OPPO手机',
        'vivo手机', '一加手机', '魅族手机', '小鹏汽车', '威马汽车', 'DJI无人机'
    ],
    
    'events': [
        '春节联欢晚会', '世界杯', '奥运会', '进博会', '双十一购物节', '618购物节',
        '开发者大会', '新品发布会', '股东大会', '年度总结会', '技术峰会', '创新大赛',
        '人工智能大会', '互联网大会', '数字经济峰会', '创业大赛', '科技博览会', '投资论坛',
        '学术会议', '行业峰会', '产品展示会', '合作签约仪式', '上市发布会', '战略发布会'
    ]
}

# 句子模板配置
SENTENCE_TEMPLATES = {
    'simple': [
        "{person}在{location}的{organization}工作",
        "{person}于{time}在{location}参加了{event}",
        "{organization}发布了新产品{product}，售价{money}",
        "{person}计划在{time}前往{location}出差",
        "{organization}将在{time}举办{event}",
        "{product}在{location}的销量突破{money}",
        "{person}和{person}共同创立了{organization}",
        "{time}，{organization}在{location}召开{event}",
        "{person}使用{product}完成了工作任务",
        "{organization}投资{money}研发{product}"
    ],
    
    'complex': [
        "{person}作为{organization}的代表，将在{time}前往{location}参加{event}，预计投资{money}用于{product}的研发。",
        "据{organization}消息，{person}在{time}成功完成了{product}项目，为公司节省了{money}的成本。",
        "{time}，{person}和团队在{location}发布了{product}，{organization}股价上涨，市值增加{money}。",
        "{organization}宣布，{person}将担任新职务，负责{product}在{location}的推广，预算为{money}。",
        "在{event}上，{person}代表{organization}展示了{product}，获得了{location}政府{money}的资助。",
        "{person}透露，{organization}计划在{time}之前，向{location}市场投入{money}，主推{product}产品线。",
        "经过{time}的努力，{person}带领{organization}团队成功将{product}推向{location}市场，获得{money}收益。",
        "{organization}CEO{person}在{event}中宣布，公司将在{time}前完成{product}的升级，投资规模达{money}。"
    ]
}

# 时间和金额格式配置
TIME_FORMATS = [
    lambda: f"{random.randint(2020, 2024)}年{random.randint(1, 12)}月{random.randint(1, 28)}日",
    lambda: f"{random.randint(1, 12)}月{random.randint(1, 28)}日", 
    lambda: f"今年{random.randint(1, 12)}月",
    lambda: f"明年{random.randint(1, 12)}月",
    lambda: f"{random.randint(2020, 2024)}年",
    lambda: f"上周{random.choice(['一', '二', '三', '四', '五', '六', '日'])}",
    lambda: f"下个月{random.randint(1, 28)}号",
    lambda: f"{random.randint(2020, 2024)}年第{random.randint(1, 4)}季度",
    lambda: f"本月{random.randint(1, 28)}号",
    lambda: f"去年{random.randint(1, 12)}月"
]

MONEY_FORMATS = [
    lambda: f"{random.randint(1, 999)}万元",
    lambda: f"{random.randint(1, 99)}亿元", 
    lambda: f"{random.randint(100, 9999)}元",
    lambda: f"{random.randint(1, 99)}.{random.randint(10, 99)}万元",
    lambda: f"{random.randint(1, 999)}万美元",
    lambda: f"{random.randint(1, 99)}亿美元",
    lambda: f"{random.randint(1, 999)}万人民币",
    lambda: f"{random.randint(100, 999)}.{random.randint(10, 99)}万元",
    lambda: f"{random.randint(1, 99)}万港币",
    lambda: f"{random.randint(1000, 9999)}万元"
]

import random
