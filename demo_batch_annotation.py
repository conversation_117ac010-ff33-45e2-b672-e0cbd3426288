#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量智能标注演示脚本
展示如何使用UI的批量标注功能
"""

import json
import os
import subprocess
import sys

def create_demo_dataset():
    """创建演示用的小数据集"""
    demo_data = [
        {"text": "张三在北京大学学习计算机科学", "source": "demo"},
        {"text": "李四在腾讯公司工作，年薪50万元", "source": "demo"},
        {"text": "王五计划明年参加奥运会比赛", "source": "demo"},
        {"text": "赵六在上海买了一台iPhone手机", "source": "demo"},
        {"text": "马云创建的阿里巴巴总部位于杭州", "source": "demo"},
        {"text": "华为P50手机在2021年发布", "source": "demo"},
        {"text": "清华大学将举办人工智能研讨会", "source": "demo"},
        {"text": "小明花费3000元购买了MacBook", "source": "demo"},
        {"text": "春节期间，很多人回到家乡过年", "source": "demo"},
        {"text": "字节跳动开发的抖音app非常受欢迎", "source": "demo"}
    ]
    
    filename = "demo_dataset_for_batch.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(demo_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 演示数据集已创建: {filename}")
    return filename

def print_instructions():
    """打印使用说明"""
    print("""
🎯 批量智能标注演示指南
=================================

📋 准备工作:
1. 确保模型已正确加载
2. 使用演示数据集测试功能

🚀 批量标注步骤:
1. 启动增强版UI: python start_enhanced_ui.py
2. 加载演示数据集: demo_dataset_for_batch.json
3. 切换到"智能标注"页面
4. 在"批量智能预标注"区域设置参数:
   • 选择标注范围（全部样本/未标注样本/自定义范围）
   • 如选择自定义范围，输入起始和结束位置
5. 点击"⚡ 开始批量智能预标注"
6. 观察进度条和统计信息
7. 可随时点击"⏹ 停止批量标注"中断处理
8. 完成后保存数据

⚡ 批量标注选项说明:
• 全部样本: 对整个数据集进行标注
• 未标注样本: 只标注没有labels字段或全为'O'标签的样本
• 自定义范围: 指定起始和结束样本编号

📊 统计信息:
• 进度显示: 当前处理数/总数 (百分比)
• 成功数量: 成功标注的样本数
• 失败数量: 标注失败的样本数  
• 发现实体: 预测到的实体总数

💡 提示:
• 批量标注会修改原始数据，建议先备份
• 可以在处理过程中查看其他页面
• 批量标注完成后记得保存数据
• 大数据集建议分批处理，避免长时间运行

🔧 故障排除:
• 如果模型未加载，检查model/best_model/目录
• 如果预测失败，查看控制台错误信息
• 内存不足时建议减少批量处理数量
""")

def main():
    """主函数"""
    print("🎯 批量智能标注演示")
    print("=" * 50)
    
    # 创建演示数据集
    demo_file = create_demo_dataset()
    
    # 打印使用说明
    print_instructions()
    
    # 询问是否启动UI
    response = input("是否立即启动增强版UI进行演示？(y/n): ").strip().lower()
    
    if response in ['y', 'yes', '是']:
        print("\n🚀 启动增强版UI...")
        try:
            # 启动UI
            subprocess.run([sys.executable, "start_enhanced_ui.py"])
        except Exception as e:
            print(f"❌ 启动UI失败: {e}")
            print("💡 请手动运行: python start_enhanced_ui.py")
    else:
        print("\n💡 手动启动UI命令: python start_enhanced_ui.py")
        print(f"💡 演示数据集位置: {os.path.abspath(demo_file)}")

if __name__ == "__main__":
    main()