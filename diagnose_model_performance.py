#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断智能标注性能问题
分析模型预测结果的准确性和实际效果
"""

import json
import sys
import os

# 添加模型路径
model_path = os.path.join(os.path.dirname(__file__), 'model')
if model_path not in sys.path:
    sys.path.append(model_path)

try:
    from model.inference import NERInference
    MODEL_AVAILABLE = True
except ImportError:
    MODEL_AVAILABLE = False
    print("❌ 无法导入模型推理模块")

def test_model_on_sample_texts():
    """测试模型在示例文本上的表现"""
    if not MODEL_AVAILABLE:
        print("❌ 模型不可用，无法进行测试")
        return
    
    # 检查模型路径
    best_model_path = os.path.join('model', 'best_model')
    chinese_model_path = os.path.join('model', 'chinese_ner_model')
    
    model_path = None
    if os.path.exists(best_model_path):
        model_path = best_model_path
    elif os.path.exists(chinese_model_path):
        model_path = chinese_model_path
    else:
        print("❌ 找不到模型文件")
        return
    
    print(f"🤖 加载模型: {model_path}")
    
    try:
        inference = NERInference(model_path)
        print("✅ 模型加载成功")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return
    
    # 测试样本
    test_samples = [
        "张三在北京大学学习",
        "李四在腾讯公司工作，年薪50万元",
        "华为手机在2023年发布",
        "明天是春节，大家都回家过年",
        "苹果公司的iPhone销量很好",
        "王五买了一台特斯拉汽车",
        "清华大学举办人工智能会议",
        "小明花了3000元买书",
        "上海是一个国际化大都市",
        "疫情期间很多人在家办公"
    ]
    
    print(f"\n🔍 测试 {len(test_samples)} 个样本:")
    print("=" * 80)
    
    total_entities = 0
    total_chars = 0
    
    for i, text in enumerate(test_samples, 1):
        print(f"\n📝 样本 {i}: {text}")
        print(f"📏 文本长度: {len(text)} 字符")
        
        try:
            result = inference.predict(text)
            
            chars = result.get('chars', [])
            labels = result.get('labels', [])
            entities = result.get('entities', [])
            confidence = result.get('confidence', [])
            
            print(f"🔤 预测字符数: {len(chars)}")
            print(f"🏷️ 标签数: {len(labels)}")
            print(f"🎯 实体数: {len(entities)}")
            
            if len(chars) != len(text):
                print(f"⚠️ 警告: 字符数不匹配! 原文{len(text)}字符，预测{len(chars)}字符")
            
            if entities:
                print("📋 识别的实体:")
                for entity in entities:
                    entity_text = entity.get('entity', '')
                    entity_type = entity.get('type', '')
                    entity_conf = entity.get('confidence', 0.0)
                    start_pos = entity.get('start', -1)
                    end_pos = entity.get('end', -1)
                    print(f"   • {entity_text} ({entity_type}) [置信度: {entity_conf:.3f}] [位置: {start_pos}-{end_pos}]")
                total_entities += len(entities)
            else:
                print("   • 未识别到任何实体")
            
            # 显示标签序列（调试用）
            print("🔍 标签序列:")
            label_parts = []
            for j in range(min(20, len(chars), len(labels))):  # 只显示前20个字符
                char = chars[j] if j < len(chars) else '?'
                label = labels[j] if j < len(labels) else '?'
                conf = confidence[j] if j < len(confidence) else 0.0
                label_parts.append(f"{char}({label}:{conf:.2f})")
            
            if len(chars) > 20:
                label_parts.append("...")
            
            print("   " + " ".join(label_parts))
            
            total_chars += len(text)
            
        except Exception as e:
            print(f"❌ 预测失败: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n📊 总体统计:")
    print(f"   • 处理文本: {total_chars} 字符")
    print(f"   • 识别实体: {total_entities} 个")
    print(f"   • 平均密度: {total_entities/len(test_samples):.1f} 实体/样本")

def analyze_label_distribution():
    """分析训练数据的标签分布"""
    print("\n🔍 分析训练数据标签分布:")
    
    # 检查训练数据
    data_files = [
        "data/ner_dataset_20250803_153737.json",
        "model/output/train_data.json",
        "test_annotated_data.json"
    ]
    
    for data_file in data_files:
        if os.path.exists(data_file):
            print(f"\n📁 分析文件: {data_file}")
            try:
                with open(data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                label_counts = {}
                entity_counts = {}
                total_chars = 0
                total_samples = len(data)
                
                for sample in data[:100]:  # 只分析前100个样本
                    labels = sample.get('labels', [])
                    total_chars += len(labels)
                    
                    for label in labels:
                        label_counts[label] = label_counts.get(label, 0) + 1
                        
                        if label.startswith('B-'):
                            entity_type = label[2:]
                            entity_counts[entity_type] = entity_counts.get(entity_type, 0) + 1
                
                print(f"   📊 样本数: {total_samples}")
                print(f"   📏 字符数: {total_chars}")
                
                print("   🏷️ 标签分布:")
                for label, count in sorted(label_counts.items()):
                    percentage = (count / total_chars) * 100
                    print(f"      {label}: {count} ({percentage:.1f}%)")
                
                print("   🎯 实体类型分布:")
                for entity_type, count in sorted(entity_counts.items()):
                    print(f"      {entity_type}: {count} 个")
                
            except Exception as e:
                print(f"   ❌ 分析失败: {e}")
        else:
            print(f"   ❌ 文件不存在: {data_file}")

def check_model_config():
    """检查模型配置"""
    print("\n🔧 检查模型配置:")
    
    config_files = [
        "model/best_model/config.json",
        "model/chinese_ner_model/config.json",
        "model/best_model/label_mapping.json",
        "model/chinese_ner_model/label_mapping.json"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"\n📄 {config_file}:")
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                if 'label_mapping' in config_file:
                    print("   🏷️ 标签映射:")
                    for key, value in config.items():
                        print(f"      {key}: {value}")
                else:
                    print("   ⚙️ 模型配置:")
                    important_keys = ['model_type', 'num_labels', 'vocab_size', 'hidden_size']
                    for key in important_keys:
                        if key in config:
                            print(f"      {key}: {config[key]}")
                            
            except Exception as e:
                print(f"   ❌ 读取失败: {e}")

def suggest_improvements():
    """建议改进方案"""
    print("""
💡 智能标注效果改进建议:
==============================

🎯 常见问题和解决方案:

1️⃣ 实体识别不准确:
   • 检查训练数据质量和标注一致性
   • 确认测试文本的领域是否与训练数据匹配
   • 考虑增加更多训练数据
   • 调整模型的置信度阈值

2️⃣ 实体边界不准确:
   • 检查BIO标签的一致性
   • 验证分词结果是否正确
   • 考虑使用字符级别的标注

3️⃣ 某些实体类型效果差:
   • 分析该类型实体在训练数据中的分布
   • 增加该类型实体的训练样本
   • 检查该类型实体的标注标准

4️⃣ 模型泛化能力不足:
   • 使用更多样化的训练数据
   • 考虑数据增强技术
   • 调整训练超参数

🔧 调试步骤:
1. 启用UI的调试模式查看详细信息
2. 手动测试几个典型样本
3. 对比模型预测和期望结果
4. 分析错误案例的规律

⚡ 快速改进:
1. 调整置信度阈值过滤低质量预测
2. 后处理规则修正常见错误
3. 针对特定领域微调模型
4. 使用集成方法提高准确性

🎛️ 参数调优:
1. 学习率: 降低学习率可能提高稳定性
2. 批次大小: 调整批次大小影响训练效果
3. 训练轮数: 增加训练轮数但注意过拟合
4. 正则化: 添加dropout等防止过拟合
""")

def main():
    """主函数"""
    print("🔍 智能标注性能诊断工具")
    print("=" * 50)
    
    # 测试模型推理
    test_model_on_sample_texts()
    
    # 分析标签分布
    analyze_label_distribution()
    
    # 检查模型配置
    check_model_config()
    
    # 提供改进建议
    suggest_improvements()
    
    print("\n🎯 诊断完成! 请根据上述分析结果优化模型和标注流程。")

if __name__ == "__main__":
    main()