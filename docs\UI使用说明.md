# NER数据标注工具使用说明

## 概述

这是一个可视化的NER（命名实体识别）数据标注工具，提供了数据浏览、可疑数据检测、手动标注修正等功能。

## 启动方法

```bash
python ner_annotation_ui.py
```

或者使用启动脚本：

```bash
python start_ui.py
```

## 界面介绍

### 主界面布局

- **工具栏**: 包含文件操作、验证等功能按钮
- **标签页**: 四个主要功能模块
- **状态栏**: 显示当前操作状态和进度

### 工具栏功能

1. **加载数据集**: 加载JSON格式的NER数据文件
2. **保存修改**: 保存对数据的修改
3. **验证数据**: 检查数据的BIO格式正确性
4. **状态显示**: 显示当前数据集状态
5. **进度条**: 显示长时间操作的进度

## 功能模块详解

### 1. 数据浏览

**用途**: 浏览和查看NER数据集中的样本

**功能**:
- 导航控制：上一条、下一条、跳转到指定样本
- 样本信息显示：ID、索引、长度、修改状态
- 彩色标注显示：不同实体类型用不同颜色高亮

**实体颜色映射**:
- PER (人名): 浅粉色
- ORG (组织): 天蓝色  
- LOC (地点): 浅绿色
- TIME (时间): 梅花色
- MONEY (金额): 卡其色
- PRODUCT (产品): 浅鲑鱼色
- EVENT (事件): 浅海绿色

**操作方法**:
1. 使用"上一条"/"下一条"按钮浏览
2. 在跳转框输入数字直接跳转
3. 使用键盘左右箭头键快速导航

### 2. 可疑数据检查

**用途**: 自动检测数据中的可疑标注

**检测类型**:
- 单字符实体：检测只有一个字符的PER、ORG、LOC实体
- 可疑人名：检测包含停用词的人名
- 其他异常模式

**操作流程**:
1. 点击"检测可疑数据"开始检测
2. 使用"上一个可疑"/"下一个可疑"浏览可疑情况
3. 对每个可疑情况进行处理：
   - **标记为正确**: 确认标注无误
   - **需要修正**: 标记需要人工修正
   - **跳转到标注页**: 直接进入手动标注模式

**可疑情况信息**:
- 样本ID和类型
- 问题描述
- 完整句子
- 实体上下文

### 3. 手动标注

**用途**: 手动修正错误的标注

**界面布局**:
- **左侧**: 句子和标签编辑区
- **右侧**: 标注规则参考

**编辑功能**:
- 句子编辑：修改原始句子文本
- 标签编辑：修改字符级别的BIO标签
- 实时验证：检查BIO格式正确性

**标注格式**:
```
字符    标签
张      B-PER
三      I-PER
在      O
北      B-ORG
京      I-ORG
大      I-ORG
学      I-ORG
```

**操作步骤**:
1. 点击"加载当前样本"载入要修改的数据
2. 在编辑区修改句子或标签
3. 点击"应用修改"保存更改
4. 使用"重置"恢复原始数据

**标注规则**:
- B-标签：实体的开始
- I-标签：实体的延续
- O标签：非实体
- I-标签前必须是对应的B-标签或I-标签

### 4. 统计信息

**用途**: 查看数据集的统计信息

**统计内容**:
- 基本信息：样本数、字符数、平均长度
- 实体分布：各类型实体的数量和比例
- 复杂度分布：简单/复杂句子比例
- 长度统计：最短/最长/中位数长度
- 修改统计：已修改样本数量
- 可疑数据统计：各类可疑情况数量

**操作**:
- 点击"刷新统计"更新最新数据

## 快捷键

- **Ctrl+O**: 打开数据集文件
- **Ctrl+S**: 保存数据集
- **左箭头**: 上一个样本
- **右箭头**: 下一个样本

## 数据格式

### 输入格式 (JSON)
```json
{
  "id": 1,
  "sentence": "张三在北京大学工作",
  "chars": ["张", "三", "在", "北", "京", "大", "学", "工", "作"],
  "tags": ["B-PER", "I-PER", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O"],
  "length": 9,
  "complexity": "simple"
}
```

### 支持的实体类型
- **PER**: 人名
- **ORG**: 组织机构
- **LOC**: 地点
- **TIME**: 时间
- **MONEY**: 金额
- **PRODUCT**: 产品
- **EVENT**: 事件

## 使用建议

### 数据检查流程
1. 加载数据集
2. 查看统计信息了解数据概况
3. 运行可疑数据检测
4. 逐一检查可疑情况
5. 使用手动标注修正错误
6. 保存修改后的数据

### 标注质量控制
1. 定期验证数据格式
2. 关注可疑数据检测结果
3. 保持标注一致性
4. 及时保存修改

### 性能优化
- 大数据集建议分批处理
- 定期保存避免数据丢失
- 可疑检测可能耗时较长，请耐心等待

## 常见问题

**Q: 界面卡顿怎么办？**
A: 大数据集操作时可能出现卡顿，建议分批处理或等待操作完成。

**Q: 修改后如何确认保存成功？**
A: 保存成功后会显示确认消息，且样本信息中的"[已修改]"标记会消失。

**Q: 可疑数据检测很慢？**
A: 这是正常现象，检测过程会遍历所有数据，请查看进度条了解进度。

**Q: 如何批量修正相似错误？**
A: 目前需要逐个修正，建议先用可疑数据检测找出问题，再集中处理。

## 技术支持

如遇到问题，请检查：
1. Python环境是否正确
2. 数据文件格式是否正确
3. 是否有足够的内存处理大数据集

更多技术细节请参考源代码注释。
