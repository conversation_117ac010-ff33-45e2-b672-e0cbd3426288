# NER标注工具图例功能说明

## 功能概述

为了让用户更直观地理解不同颜色代表的实体类型，我们在UI界面中添加了实体类型图例功能。

## 图例位置

图例在以下三个位置显示：

### 1. 数据浏览页面
- **位置**: 句子标注显示框的上方
- **样式**: 完整图例，包含颜色块和图标
- **用途**: 帮助用户理解句子中彩色标注的含义

### 2. 可疑数据检查页面
- **位置**: 可疑数据详情框的上方
- **样式**: 完整图例
- **用途**: 在检查可疑数据时提供颜色参考

### 3. 手动标注页面
- **位置**: 标注参考区域的顶部
- **样式**: 紧凑图例
- **用途**: 在手动标注时提供快速颜色参考

## 图例设计

### 完整图例
```
🏷️ 实体类型图例:
┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐
│   PER   │ │   ORG   │ │   LOC   │ │  TIME   │
│ 👤人名  │ │ 🏢组织  │ │ 📍地点  │ │ ⏰时间  │
└─────────┘ └─────────┘ └─────────┘ └─────────┘
┌─────────┐ ┌─────────┐ ┌─────────┐
│  MONEY  │ │ PRODUCT │ │  EVENT  │
│ 💰金额  │ │ 📱产品  │ │ 🎯事件  │
└─────────┘ └─────────┘ └─────────┘
```

### 紧凑图例
```
🎨 颜色图例: [PER:人名] [ORG:组织] [LOC:地点] [TIME:时间] [MONEY:金额] [PRODUCT:产品] [EVENT:事件]
```

## 颜色映射

| 实体类型 | 颜色代码 | 颜色名称 | 图标 | 说明 |
|---------|---------|---------|------|------|
| PER | #FFB6C1 | 浅粉色 | 👤 | 人名实体 |
| ORG | #87CEEB | 天蓝色 | 🏢 | 组织机构 |
| LOC | #98FB98 | 浅绿色 | 📍 | 地点位置 |
| TIME | #DDA0DD | 梅花色 | ⏰ | 时间表达 |
| MONEY | #F0E68C | 卡其色 | 💰 | 金额数值 |
| PRODUCT | #FFA07A | 浅鲑鱼色 | 📱 | 产品名称 |
| EVENT | #20B2AA | 浅海绿色 | 🎯 | 事件活动 |

## 使用方法

### 1. 查看图例
- 启动UI后，图例会自动显示在相应位置
- 无需额外操作，图例始终可见

### 2. 对照标注
- 在句子中看到彩色标注时，参考图例了解实体类型
- 图例颜色与句子标注颜色完全一致

### 3. 标注参考
- 在手动标注时，可参考图例选择正确的实体类型
- 紧凑图例提供快速颜色参考

## 测试数据

使用以下命令创建包含所有实体类型的测试数据：

```bash
python test_legend.py
```

测试数据包含：
- **PER**: 张三、李四、王五、赵六、孙七
- **ORG**: 北京大学、腾讯科技公司
- **LOC**: 上海市、深圳市
- **TIME**: 2024年1月、明年3月
- **MONEY**: 5000元、8000元、100万元
- **PRODUCT**: iPhone 15、微信
- **EVENT**: 春节联欢晚会、科技博览会

## 优势特点

### 1. 直观性
- 颜色与图标结合，一目了然
- 中文描述，易于理解

### 2. 一致性
- 图例颜色与标注颜色完全一致
- 所有页面使用统一的颜色方案

### 3. 便利性
- 图例始终可见，无需切换页面
- 紧凑设计，不占用过多空间

### 4. 美观性
- 现代化的UI设计
- 合理的颜色搭配
- 清晰的边框和布局

## 技术实现

### 颜色配置
```python
self.entity_colors = {
    'PER': '#FFB6C1',      # 浅粉色
    'ORG': '#87CEEB',      # 天蓝色
    'LOC': '#98FB98',      # 浅绿色
    'TIME': '#DDA0DD',     # 梅花色
    'MONEY': '#F0E68C',    # 卡其色
    'PRODUCT': '#FFA07A',  # 浅鲑鱼色
    'EVENT': '#20B2AA'     # 浅海绿色
}
```

### 图例创建
- `create_legend()`: 创建完整图例
- `create_compact_legend()`: 创建紧凑图例

## 使用建议

1. **初次使用**: 先查看图例，熟悉颜色映射关系
2. **数据浏览**: 结合图例理解句子中的实体标注
3. **质量检查**: 利用图例快速识别标注错误
4. **手动标注**: 参考图例选择正确的实体类型

## 未来改进

1. **自定义颜色**: 允许用户自定义实体类型颜色
2. **图例位置**: 提供图例显示/隐藏选项
3. **颜色主题**: 支持多种颜色主题切换
4. **无障碍设计**: 为色盲用户提供替代方案

图例功能大大提升了NER标注工具的用户体验，让数据标注工作更加直观和高效！
