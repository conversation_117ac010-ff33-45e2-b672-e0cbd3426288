#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版NER数据生成器 - 使用配置文件，生成更高质量的字符级别BIO标注数据
"""

import random
import json
import re
from typing import List, Tuple, Dict
from datetime import datetime
from config import DATASET_CONFIG, ENTITY_TYPES, EXTENDED_VOCAB, SENTENCE_TEMPLATES, TIME_FORMATS, MONEY_FORMATS

class EnhancedNERGenerator:
    def __init__(self, config=None):
        self.config = config or DATASET_CONFIG
        self.entity_types = ENTITY_TYPES
        self.vocab = EXTENDED_VOCAB
        self.templates = SENTENCE_TEMPLATES
        
        # 设置随机种子以确保结果可复现
        if 'random_seed' in self.config:
            random.seed(self.config['random_seed'])
    
    def generate_person_name(self) -> str:
        """生成人名"""
        surname = random.choice(self.vocab['surnames'])
        given_name = random.choice(self.vocab['given_names'])
        
        # 30%概率生成两字名，20%概率生成单字名
        if random.random() < 0.3:
            given_name += random.choice(self.vocab['given_names'])
        elif random.random() < 0.2:
            given_name = random.choice(['军', '华', '明', '强', '伟', '芳', '丽', '娜'])
            
        return surname + given_name
    
    def generate_time(self) -> str:
        """生成时间表达"""
        return random.choice(TIME_FORMATS)()
    
    def generate_money(self) -> str:
        """生成金额表达"""
        return random.choice(MONEY_FORMATS)()
    
    def generate_sentence(self, complexity='simple') -> str:
        """生成句子"""
        template = random.choice(self.templates[complexity])

        # 准备实体数据
        entities = {
            'person': self.generate_person_name(),
            'location': random.choice(self.vocab['locations']),
            'organization': random.choice(self.vocab['organizations']),
            'time': self.generate_time(),
            'money': self.generate_money(),
            'product': random.choice(self.vocab['products']),
            'event': random.choice(self.vocab['events'])
        }

        # 处理多个person的情况
        person_count = template.count('{person}')
        if person_count > 1:
            persons = [self.generate_person_name() for _ in range(person_count)]
            template_parts = template.split('{person}')
            sentence = template_parts[0]
            for i, person in enumerate(persons):
                sentence += person
                if i + 1 < len(template_parts):
                    sentence += template_parts[i + 1]
            template = sentence
        else:
            # 替换单个person占位符
            template = template.replace('{person}', entities['person'])

        # 替换其他占位符
        for key, value in entities.items():
            if key != 'person':
                template = template.replace(f'{{{key}}}', value)

        return template
    
    def char_level_bio_tagging(self, sentence: str) -> List[Tuple[str, str]]:
        """对句子进行字符级别的BIO标注"""
        chars = list(sentence)
        tags = ['O'] * len(chars)
        
        # 标注已知实体
        entity_patterns = {
            'PER': self.vocab['surnames'] + self.vocab['given_names'],
            'ORG': self.vocab['organizations'],
            'LOC': self.vocab['locations'],
            'PRODUCT': self.vocab['products'],
            'EVENT': self.vocab['events']
        }
        
        # 标注实体（从长到短排序，避免短实体覆盖长实体）
        for entity_type, entities in entity_patterns.items():
            sorted_entities = sorted(entities, key=len, reverse=True)
            for entity in sorted_entities:
                start = 0
                while True:
                    pos = sentence.find(entity, start)
                    if pos == -1:
                        break
                    
                    # 检查是否已被标注
                    if all(tags[pos + i] == 'O' for i in range(len(entity))):
                        # 标注实体
                        for i in range(len(entity)):
                            if i == 0:
                                tags[pos + i] = f'B-{entity_type}'
                            else:
                                tags[pos + i] = f'I-{entity_type}'
                    
                    start = pos + 1
        
        # 使用正则表达式标注时间和金额
        patterns = [
            (r'\d{4}年\d{1,2}月\d{1,2}日', 'TIME'),
            (r'\d{1,2}月\d{1,2}日', 'TIME'),
            (r'今年\d{1,2}月', 'TIME'),
            (r'明年\d{1,2}月', 'TIME'),
            (r'\d{4}年', 'TIME'),
            (r'上周[一二三四五六日]', 'TIME'),
            (r'下个月\d{1,2}号', 'TIME'),
            (r'本月\d{1,2}号', 'TIME'),
            (r'\d{4}年第\d季度', 'TIME'),
            (r'\d+\.?\d*万元', 'MONEY'),
            (r'\d+\.?\d*亿元', 'MONEY'),
            (r'\d+元', 'MONEY'),
            (r'\d+\.?\d*万美元', 'MONEY'),
            (r'\d+\.?\d*亿美元', 'MONEY'),
            (r'\d+\.?\d*万人民币', 'MONEY'),
            (r'\d+\.?\d*万港币', 'MONEY')
        ]
        
        for pattern, entity_type in patterns:
            for match in re.finditer(pattern, sentence):
                start, end = match.span()
                # 检查是否已被标注
                if all(tags[i] == 'O' for i in range(start, end)):
                    for i in range(start, end):
                        if i == start:
                            tags[i] = f'B-{entity_type}'
                        else:
                            tags[i] = f'I-{entity_type}'
        
        return list(zip(chars, tags))
    
    def generate_dataset(self, num_samples: int = None) -> List[Dict]:
        """生成数据集"""
        if num_samples is None:
            num_samples = self.config['num_samples']
        
        dataset = []
        complex_ratio = self.config.get('complex_sentence_ratio', 0.7)
        
        print(f"开始生成{num_samples}条NER数据...")
        
        for i in range(num_samples):
            if i % 1000 == 0:
                print(f"已生成 {i}/{num_samples} 条数据")
            
            # 根据配置决定句子复杂度
            complexity = 'complex' if random.random() < complex_ratio else 'simple'
            sentence = self.generate_sentence(complexity)
            
            # 进行BIO标注
            char_tags = self.char_level_bio_tagging(sentence)
            
            # 构造数据样本
            sample = {
                'id': i + 1,
                'sentence': sentence,
                'chars': [char for char, _ in char_tags],
                'tags': [tag for _, tag in char_tags],
                'length': len(sentence),
                'complexity': complexity
            }
            
            dataset.append(sample)
        
        print(f"数据生成完成！共生成{len(dataset)}条数据")
        return dataset
    
    def save_dataset(self, dataset: List[Dict], output_formats: List[str] = None):
        """保存数据集到文件"""
        if output_formats is None:
            output_formats = self.config.get('output_formats', ['json'])
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        for format_type in output_formats:
            if format_type == 'json':
                filename = f"ner_dataset_{timestamp}.json"
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(dataset, f, ensure_ascii=False, indent=2)
                print(f"JSON格式数据集已保存到: {filename}")
                
            elif format_type == 'conll':
                filename = f"ner_dataset_{timestamp}.conll"
                with open(filename, 'w', encoding='utf-8') as f:
                    for sample in dataset:
                        f.write(f"# Sentence {sample['id']}: {sample['sentence']}\n")
                        for char, tag in zip(sample['chars'], sample['tags']):
                            f.write(f"{char}\t{tag}\n")
                        f.write("\n")
                print(f"CoNLL格式数据集已保存到: {filename}")
                
            elif format_type == 'bio':
                filename = f"ner_dataset_{timestamp}.bio"
                with open(filename, 'w', encoding='utf-8') as f:
                    for sample in dataset:
                        chars_line = " ".join(sample['chars'])
                        tags_line = " ".join(sample['tags'])
                        f.write(f"{chars_line}\n{tags_line}\n\n")
                print(f"BIO格式数据集已保存到: {filename}")
    
    def analyze_dataset(self, dataset: List[Dict]):
        """分析数据集统计信息"""
        total_samples = len(dataset)
        total_chars = sum(sample['length'] for sample in dataset)
        avg_length = total_chars / total_samples
        
        # 统计复杂度分布
        complexity_counts = {}
        for sample in dataset:
            complexity = sample.get('complexity', 'unknown')
            complexity_counts[complexity] = complexity_counts.get(complexity, 0) + 1
        
        # 统计实体类型分布
        entity_counts = {}
        for sample in dataset:
            for tag in sample['tags']:
                if tag != 'O':
                    entity_type = tag.split('-')[1] if '-' in tag else tag
                    entity_counts[entity_type] = entity_counts.get(entity_type, 0) + 1
        
        print("\n=== 数据集统计信息 ===")
        print(f"总样本数: {total_samples}")
        print(f"总字符数: {total_chars}")
        print(f"平均句子长度: {avg_length:.2f} 字符")
        print(f"句子复杂度分布:")
        for complexity, count in complexity_counts.items():
            print(f"  {complexity}: {count} 条 ({count/total_samples*100:.1f}%)")
        print(f"实体类型分布:")
        for entity_type, count in sorted(entity_counts.items()):
            print(f"  {entity_type}: {count} 个")


def main():
    """主函数"""
    generator = EnhancedNERGenerator()
    
    # 生成数据集
    dataset = generator.generate_dataset()
    
    # 分析数据集
    generator.analyze_dataset(dataset)
    
    # 保存数据集
    generator.save_dataset(dataset)
    
    # 显示样本
    print("\n=== 数据样本展示 ===")
    for i in range(3):
        sample = dataset[i]
        print(f"\n样本 {sample['id']} ({sample['complexity']}):")
        print(f"句子: {sample['sentence']}")
        print("实体标注:")
        current_entity = ""
        current_type = ""
        for char, tag in zip(sample['chars'], sample['tags']):
            if tag.startswith('B-'):
                if current_entity:
                    print(f"  {current_entity}: {current_type}")
                current_entity = char
                current_type = tag[2:]
            elif tag.startswith('I-') and current_entity:
                current_entity += char
            else:
                if current_entity:
                    print(f"  {current_entity}: {current_type}")
                    current_entity = ""
                    current_type = ""
        if current_entity:
            print(f"  {current_entity}: {current_type}")


if __name__ == "__main__":
    main()
