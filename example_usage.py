#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NER数据生成器使用示例
"""

from enhanced_ner_generator import EnhancedNERGenerator
from config import DATASET_CONFIG

def generate_small_dataset():
    """生成小规模数据集用于测试"""
    print("=== 生成小规模测试数据集 ===")
    
    # 创建生成器
    config = DATASET_CONFIG.copy()
    config['num_samples'] = 100  # 生成100条数据用于测试
    
    generator = EnhancedNERGenerator(config)
    
    # 生成数据
    dataset = generator.generate_dataset()
    
    # 分析数据
    generator.analyze_dataset(dataset)
    
    # 保存为JSON格式
    generator.save_dataset(dataset, ['json'])
    
    return dataset

def generate_full_dataset():
    """生成完整的20000条数据集"""
    print("=== 生成完整数据集 ===")
    
    generator = EnhancedNERGenerator()
    
    # 生成20000条数据
    dataset = generator.generate_dataset(20000)
    
    # 分析数据
    generator.analyze_dataset(dataset)
    
    # 保存为多种格式
    generator.save_dataset(dataset, ['json', 'conll', 'bio'])
    
    return dataset

def custom_generation():
    """自定义生成配置"""
    print("=== 自定义配置生成 ===")
    
    custom_config = {
        'num_samples': 5000,
        'complex_sentence_ratio': 0.8,  # 80%复杂句子
        'output_formats': ['json', 'conll'],
        'random_seed': 123
    }
    
    generator = EnhancedNERGenerator(custom_config)
    dataset = generator.generate_dataset()
    generator.analyze_dataset(dataset)
    generator.save_dataset(dataset)
    
    return dataset

def show_sample_data(dataset, num_samples=5):
    """展示样本数据"""
    print(f"\n=== 展示前{num_samples}个样本 ===")
    
    for i in range(min(num_samples, len(dataset))):
        sample = dataset[i]
        print(f"\n样本 {sample['id']}:")
        print(f"句子: {sample['sentence']}")
        print(f"长度: {sample['length']} 字符")
        print(f"复杂度: {sample['complexity']}")
        
        # 提取实体
        entities = []
        current_entity = ""
        current_type = ""
        
        for char, tag in zip(sample['chars'], sample['tags']):
            if tag.startswith('B-'):
                if current_entity:
                    entities.append((current_entity, current_type))
                current_entity = char
                current_type = tag[2:]
            elif tag.startswith('I-') and current_entity:
                current_entity += char
            else:
                if current_entity:
                    entities.append((current_entity, current_type))
                    current_entity = ""
                    current_type = ""
        
        if current_entity:
            entities.append((current_entity, current_type))
        
        print("实体:")
        for entity, entity_type in entities:
            print(f"  {entity} -> {entity_type}")

def validate_bio_format(dataset, num_check=10):
    """验证BIO格式的正确性"""
    print(f"\n=== 验证BIO格式 (检查前{num_check}个样本) ===")
    
    errors = []
    
    for i in range(min(num_check, len(dataset))):
        sample = dataset[i]
        tags = sample['tags']
        
        for j, tag in enumerate(tags):
            if tag.startswith('I-'):
                # I标签前面应该是B标签或同类型的I标签
                if j == 0:
                    errors.append(f"样本{sample['id']}: 第{j+1}个字符'{sample['chars'][j]}'的标签'{tag}'不能出现在句子开头")
                else:
                    prev_tag = tags[j-1]
                    expected_types = [f"B-{tag[2:]}", f"I-{tag[2:]}"]
                    if prev_tag not in expected_types:
                        errors.append(f"样本{sample['id']}: 第{j+1}个字符'{sample['chars'][j]}'的标签'{tag}'前面应该是{expected_types}之一，但实际是'{prev_tag}'")
    
    if errors:
        print("发现BIO格式错误:")
        for error in errors[:5]:  # 只显示前5个错误
            print(f"  {error}")
        if len(errors) > 5:
            print(f"  ... 还有{len(errors)-5}个错误")
    else:
        print("BIO格式验证通过！")

def main():
    """主函数 - 演示不同的使用方式"""
    print("NER数据生成器使用示例")
    print("=" * 50)
    
    # 选择运行模式
    mode = input("请选择运行模式:\n1. 生成小规模测试数据(100条)\n2. 生成完整数据集(20000条)\n3. 自定义配置生成\n请输入选项(1/2/3): ")
    
    if mode == '1':
        dataset = generate_small_dataset()
        show_sample_data(dataset, 5)
        validate_bio_format(dataset, 10)
        
    elif mode == '2':
        dataset = generate_full_dataset()
        show_sample_data(dataset, 3)
        validate_bio_format(dataset, 20)
        
    elif mode == '3':
        dataset = custom_generation()
        show_sample_data(dataset, 3)
        validate_bio_format(dataset, 15)
        
    else:
        print("无效选项，默认生成小规模测试数据")
        dataset = generate_small_dataset()
        show_sample_data(dataset, 5)
        validate_bio_format(dataset, 10)
    
    print("\n数据生成完成！")

if __name__ == "__main__":
    main()
