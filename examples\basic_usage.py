#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NER数据生成器基本使用示例
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.ner_generator import NERGenerator
from src.config import DATASET_CONFIG
from utils.validator import DataValidator

def generate_sample_data():
    """生成示例数据"""
    print("=== 生成示例数据 ===")
    
    # 创建生成器
    config = DATASET_CONFIG.copy()
    config['num_samples'] = 100  # 生成100条示例数据
    
    generator = NERGenerator(config)
    
    # 生成数据集
    dataset = generator.generate_dataset()
    
    # 分析数据
    generator.analyze_dataset(dataset)
    
    # 保存数据
    generator.save_dataset(dataset, output_dir="../data", formats=['json', 'bio'])
    
    return dataset

def generate_full_data():
    """生成完整数据集"""
    print("=== 生成完整数据集 ===")
    
    generator = NERGenerator()
    dataset = generator.generate_dataset(20000)
    generator.analyze_dataset(dataset)
    generator.save_dataset(dataset, output_dir="../data")
    
    return dataset

def validate_data():
    """验证数据质量"""
    print("=== 验证数据质量 ===")
    
    # 查找最新的数据文件
    import glob
    data_files = glob.glob("../data/ner_dataset_*.json")
    if data_files:
        latest_file = max(data_files)
        DataValidator.validate_dataset(latest_file)
    else:
        print("未找到数据文件")

def show_samples(dataset, num=3):
    """展示数据样本"""
    print(f"\n=== 数据样本展示 ({num}个) ===")
    
    for i in range(min(num, len(dataset))):
        sample = dataset[i]
        print(f"\n样本 {sample['id']}:")
        print(f"句子: {sample['sentence']}")
        print(f"长度: {sample['length']} 字符")
        
        # 提取实体
        entities = []
        current_entity = ""
        current_type = ""
        
        for char, tag in zip(sample['chars'], sample['tags']):
            if tag.startswith('B-'):
                if current_entity:
                    entities.append((current_entity, current_type))
                current_entity = char
                current_type = tag[2:]
            elif tag.startswith('I-') and current_entity:
                current_entity += char
            else:
                if current_entity:
                    entities.append((current_entity, current_type))
                    current_entity = ""
                    current_type = ""
        
        if current_entity:
            entities.append((current_entity, current_type))
        
        print("实体:")
        for entity, entity_type in entities:
            print(f"  {entity} -> {entity_type}")

def main():
    """主函数"""
    print("NER数据生成器使用示例")
    print("=" * 50)
    
    choice = input("请选择操作:\n1. 生成示例数据(100条)\n2. 生成完整数据(20000条)\n3. 验证数据质量\n请输入选项(1/2/3): ")
    
    if choice == '1':
        dataset = generate_sample_data()
        show_samples(dataset, 5)
        
    elif choice == '2':
        dataset = generate_full_data()
        show_samples(dataset, 3)
        
    elif choice == '3':
        validate_data()
        
    else:
        print("无效选项，生成示例数据")
        dataset = generate_sample_data()
        show_samples(dataset, 3)

if __name__ == "__main__":
    main()
