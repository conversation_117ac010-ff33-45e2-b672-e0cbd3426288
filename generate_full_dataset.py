#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成完整的20000条NER数据集
"""

from enhanced_ner_generator import EnhancedNERGenerator
import time

def main():
    """生成完整数据集"""
    print("开始生成20000条NER训练数据...")
    print("=" * 50)
    
    start_time = time.time()
    
    # 创建生成器
    generator = EnhancedNERGenerator()
    
    # 生成数据集
    dataset = generator.generate_dataset(20000)
    
    # 分析数据集
    generator.analyze_dataset(dataset)
    
    # 保存数据集（多种格式）
    print("\n保存数据集...")
    generator.save_dataset(dataset, ['json', 'conll', 'bio'])
    
    # 显示几个样本
    print("\n=== 数据样本展示 ===")
    for i in range(5):
        sample = dataset[i]
        print(f"\n样本 {sample['id']} ({sample['complexity']}):")
        print(f"句子: {sample['sentence']}")
        
        # 提取实体
        entities = []
        current_entity = ""
        current_type = ""
        
        for char, tag in zip(sample['chars'], sample['tags']):
            if tag.startswith('B-'):
                if current_entity:
                    entities.append((current_entity, current_type))
                current_entity = char
                current_type = tag[2:]
            elif tag.startswith('I-') and current_entity:
                current_entity += char
            else:
                if current_entity:
                    entities.append((current_entity, current_type))
                    current_entity = ""
                    current_type = ""
        
        if current_entity:
            entities.append((current_entity, current_type))
        
        print("实体:")
        for entity, entity_type in entities:
            print(f"  {entity} -> {entity_type}")
    
    end_time = time.time()
    print(f"\n数据生成完成！耗时: {end_time - start_time:.2f} 秒")
    print("生成的文件:")
    print("- ner_dataset_*.json (JSON格式)")
    print("- ner_dataset_*.conll (CoNLL格式)")
    print("- ner_dataset_*.bio (BIO格式)")

if __name__ == "__main__":
    main()
