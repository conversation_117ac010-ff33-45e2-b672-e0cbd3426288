#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成测试数据集 - 用于模型推理性能测试
生成2万条不同类型的中文文本，包含各种实体
"""

import json
import random
from datetime import datetime, timedelta
import os

class TestDataGenerator:
    def __init__(self):
        # 人名库
        self.surnames = [
            "王", "李", "张", "刘", "陈", "杨", "赵", "黄", "周", "吴",
            "徐", "孙", "胡", "朱", "高", "林", "何", "郭", "马", "罗",
            "梁", "宋", "郑", "谢", "韩", "唐", "冯", "于", "董", "萧"
        ]
        
        self.given_names = [
            "伟", "芳", "娜", "秀英", "敏", "静", "丽", "强", "磊", "军",
            "洋", "勇", "艳", "杰", "涛", "明", "超", "秀兰", "霞", "平",
            "刚", "桂英", "建华", "文", "华", "志强", "秀梅", "海燕", "大伟", "丽娜",
            "晓明", "小红", "建国", "红梅", "春花", "国强", "志明", "秀华", "建军", "丽丽",
            "雨晨", "思涵", "浩然", "梦琪", "子轩", "欣怡", "宇轩", "雨萱", "佳音", "诗涵"
        ]
        
        # 组织机构库
        self.organizations = [
            "清华大学", "北京大学", "复旦大学", "上海交通大学", "浙江大学", "南京大学",
            "中山大学", "华中科技大学", "哈尔滨工业大学", "西安交通大学", "北京师范大学",
            "中国科学院", "中国社会科学院", "北京理工大学", "大连理工大学", "东南大学",
            "腾讯公司", "阿里巴巴", "百度公司", "华为技术", "字节跳动", "美团", "滴滴出行",
            "小米科技", "京东集团", "网易公司", "新浪微博", "搜狐公司", "360公司",
            "中国银行", "工商银行", "建设银行", "农业银行", "招商银行", "民生银行",
            "中国移动", "中国联通", "中国电信", "国家电网", "中石油", "中石化",
            "教育部", "科技部", "文化部", "卫生部", "财政部", "商务部", "外交部",
            "北京市政府", "上海市政府", "广州市政府", "深圳市政府", "杭州市政府"
        ]
        
        # 地点库
        self.locations = [
            "北京", "上海", "广州", "深圳", "杭州", "南京", "苏州", "成都", "重庆", "武汉",
            "西安", "天津", "青岛", "大连", "厦门", "宁波", "无锡", "佛山", "东莞", "泉州",
            "长沙", "郑州", "石家庄", "济南", "哈尔滨", "长春", "沈阳", "太原", "合肥", "南昌",
            "福州", "贵阳", "昆明", "南宁", "海口", "银川", "西宁", "兰州", "乌鲁木齐", "拉萨",
            "中关村", "陆家嘴", "前海", "浦东新区", "滨海新区", "光谷", "高新区", "经开区",
            "人民广场", "天安门", "故宫", "长城", "颐和园", "天坛", "明十三陵", "鸟巢", "水立方"
        ]
        
        # 产品库
        self.products = [
            "iPhone", "iPad", "MacBook", "AirPods", "Apple Watch", "iMac",
            "华为P系列", "华为Mate", "小米手机", "OPPO", "vivo", "一加手机",
            "特斯拉Model S", "宝马X5", "奔驰S级", "奥迪A8", "比亚迪汉", "蔚来ES8",
            "微信", "QQ", "支付宝", "钉钉", "抖音", "快手", "B站", "知乎", "豆瓣",
            "Windows", "Office", "Photoshop", "AutoCAD", "Maya", "Unity",
            "可口可乐", "百事可乐", "王老吉", "加多宝", "农夫山泉", "怡宝", "康师傅",
            "Nike", "Adidas", "安踏", "李宁", "特步", "361度", "匹克"
        ]
        
        # 事件库
        self.events = [
            "春节", "中秋节", "国庆节", "元旦", "劳动节", "端午节", "清明节", "重阳节",
            "奥运会", "世界杯", "亚运会", "全运会", "大运会", "冬奥会", "残奥会",
            "互联网大会", "博鳌论坛", "达沃斯论坛", "进博会", "广交会", "高交会",
            "开学典礼", "毕业典礼", "校庆", "年会", "发布会", "签约仪式", "颁奖典礼",
            "学术会议", "研讨会", "论坛", "峰会", "展览会", "交易会", "招聘会"
        ]
        
        # 文本模板库
        self.templates = [
            # 新闻报道类
            "{person}在{location}参加了{event}，并发表了重要讲话。",
            "{organization}在{location}举办{event}，吸引了众多参与者。",
            "{person}代表{organization}出席在{location}举行的{event}。",
            "昨天，{person}在{organization}宣布推出新产品{product}。",
            "{event}将于明年在{location}举办，预计投资{money}。",
            
            # 商业资讯类
            "{organization}CEO{person}表示，公司计划在{location}投资{money}。",
            "{product}在{location}正式发布，售价{money}。",
            "{person}创立的{organization}获得{money}融资。",
            "{organization}与{organization}在{location}签署合作协议。",
            "{product}销量突破100万台，为{organization}带来{money}收入。",
            
            # 学术教育类
            "{person}教授在{organization}发表关于{product}的研究报告。",
            "{organization}将于{time}在{location}举办{event}。",
            "{person}获得{organization}{money}研究基金支持。",
            "{organization}学生{person}在{event}中获得第一名。",
            "{organization}与{organization}合作开发{product}项目。",
            
            # 生活社交类
            "{person}和朋友们计划{time}去{location}旅游。",
            "我在{location}买了{product}，花了{money}。",
            "{person}邀请大家参加{time}的{event}聚会。",
            "今天在{location}遇到了{organization}的{person}。",
            "{event}期间，{location}的{product}销售火爆。",
            
            # 科技创新类
            "{organization}推出的{product}采用了最新技术。",
            "{person}在{event}上展示了{organization}的{product}。",
            "{organization}投资{money}研发{product}新功能。",
            "{location}成为{product}的重要生产基地。",
            "{person}领导的团队开发出革命性{product}。",
            
            # 文化娱乐类
            "{person}将在{location}举办{event}演出。",
            "{event}在{location}盛大开幕，{person}出席。",
            "{organization}制作的{product}在{event}首映。",
            "{person}荣获{event}最佳{product}奖。",
            "{location}的{event}吸引了{person}等明星参与。"
        ]

    def generate_person_name(self):
        """生成人名"""
        surname = random.choice(self.surnames)
        if random.random() < 0.7:  # 70%概率生成双字名
            given_name = random.choice(self.given_names)
        else:  # 30%概率生成单字名
            given_name = random.choice([name[0] for name in self.given_names])
        return surname + given_name

    def generate_money_amount(self):
        """生成金钱数额"""
        money_templates = [
            "{amount}元", "{amount}万元", "{amount}亿元", "{amount}美元",
            "{amount}万美元", "{amount}亿美元", "{amount}人民币",
            "{amount}万人民币", "{amount}亿人民币"
        ]
        
        amount = random.choice([
            str(random.randint(1, 9999)),
            str(random.randint(1, 999)) + "." + str(random.randint(1, 9)),
            str(random.randint(10, 999)),
            str(random.randint(1000, 9999))
        ])
        
        template = random.choice(money_templates)
        return template.format(amount=amount)

    def generate_time_expression(self):
        """生成时间表达"""
        time_templates = [
            "2023年", "2024年", "2025年", "今年", "明年", "去年",
            "春季", "夏季", "秋季", "冬季", "上半年", "下半年",
            "1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月",
            "本月", "下月", "上月", "月初", "月末", "月中",
            "本周", "下周", "上周", "周末", "工作日",
            "今天", "明天", "昨天", "后天", "前天",
            "上午", "下午", "晚上", "深夜", "凌晨",
            "2023年3月15日", "2024年春节", "2023年国庆节"
        ]
        return random.choice(time_templates)

    def generate_sample(self):
        """生成一个测试样本"""
        template = random.choice(self.templates)
        
        # 为模板填充实体
        sample_data = {
            'person': self.generate_person_name(),
            'organization': random.choice(self.organizations),
            'location': random.choice(self.locations),
            'product': random.choice(self.products),
            'event': random.choice(self.events),
            'money': self.generate_money_amount(),
            'time': self.generate_time_expression()
        }
        
        # 生成文本
        try:
            text = template.format(**sample_data)
        except KeyError:
            # 如果模板中有未定义的变量，使用简单模板
            text = f"{sample_data['person']}在{sample_data['location']}工作。"
        
        return {
            'text': text,
            'source': 'generated',
            'entities_info': sample_data
        }

    def add_variation(self, text):
        """为文本添加变化"""
        variations = [
            # 添加标点符号变化
            text,
            text + "。",
            text + "！",
            text + "？",
            
            # 添加前缀
            "据悉，" + text,
            "记者获悉，" + text,
            "消息显示，" + text,
            "最新消息，" + text,
            
            # 添加后缀
            text + "，引起广泛关注",
            text + "，备受期待",
            text + "，成为热门话题",
            text + "，值得关注"
        ]
        return random.choice(variations)

    def generate_dataset(self, num_samples=20000):
        """生成完整数据集"""
        dataset = []
        
        print(f"🚀 开始生成 {num_samples} 条测试数据...")
        
        for i in range(num_samples):
            if (i + 1) % 1000 == 0:
                print(f"📊 已生成 {i + 1}/{num_samples} 条数据 ({(i+1)/num_samples*100:.1f}%)")
            
            sample = self.generate_sample()
            
            # 添加文本变化
            if random.random() < 0.3:  # 30%概率添加变化
                sample['text'] = self.add_variation(sample['text'])
            
            # 添加样本ID
            sample['id'] = i + 1
            
            dataset.append(sample)
        
        return dataset

    def save_dataset(self, dataset, filename):
        """保存数据集"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(dataset, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 数据集已保存到: {filename}")

    def generate_statistics(self, dataset):
        """生成数据集统计信息"""
        total_samples = len(dataset)
        text_lengths = [len(sample['text']) for sample in dataset]
        avg_length = sum(text_lengths) / len(text_lengths)
        min_length = min(text_lengths)
        max_length = max(text_lengths)
        
        # 统计实体类型分布
        entity_types = {
            'person': 0, 'organization': 0, 'location': 0,
            'product': 0, 'event': 0, 'money': 0, 'time': 0
        }
        
        for sample in dataset:
            entities = sample.get('entities_info', {})
            for entity_type in entity_types:
                if entity_type in entities:
                    entity_types[entity_type] += 1
        
        stats = {
            'total_samples': total_samples,
            'text_length': {
                'average': round(avg_length, 2),
                'min': min_length,
                'max': max_length
            },
            'entity_distribution': entity_types,
            'generation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        return stats

def main():
    """主函数"""
    print("🎯 测试数据集生成器")
    print("=" * 50)
    
    # 创建生成器
    generator = TestDataGenerator()
    
    # 生成数据集
    dataset = generator.generate_dataset(20000)
    
    # 保存数据集
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"test_dataset_{timestamp}.json"
    generator.save_dataset(dataset, filename)
    
    # 生成统计信息
    stats = generator.generate_statistics(dataset)
    
    # 保存统计信息
    stats_filename = f"test_dataset_stats_{timestamp}.json"
    with open(stats_filename, 'w', encoding='utf-8') as f:
        json.dump(stats, f, ensure_ascii=False, indent=2)
    
    print(f"📊 统计信息已保存到: {stats_filename}")
    
    # 显示统计信息
    print("\n📈 数据集统计信息:")
    print(f"📋 总样本数: {stats['total_samples']:,}")
    print(f"📏 平均文本长度: {stats['text_length']['average']} 字符")
    print(f"📏 文本长度范围: {stats['text_length']['min']} - {stats['text_length']['max']} 字符")
    
    print("\n🏷️ 实体类型分布:")
    for entity_type, count in stats['entity_distribution'].items():
        percentage = (count / stats['total_samples']) * 100
        print(f"   {entity_type}: {count:,} ({percentage:.1f}%)")
    
    print(f"\n⏰ 生成时间: {stats['generation_time']}")
    
    # 显示几个示例
    print("\n📝 样本示例:")
    for i in range(min(5, len(dataset))):
        print(f"   {i+1}. {dataset[i]['text']}")
    
    print("\n🎉 测试数据集生成完成！")
    print(f"💡 使用方法: 可以直接用这个数据集测试模型推理性能")

if __name__ == "__main__":
    main()