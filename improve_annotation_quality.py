#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进智能标注质量
实现后处理规则和置信度过滤
"""

import json
import sys
import os
import re

# 添加模型路径
model_path = os.path.join(os.path.dirname(__file__), 'model')
if model_path not in sys.path:
    sys.path.append(model_path)

try:
    from model.inference import NERInference
    MODEL_AVAILABLE = True
except ImportError:
    MODEL_AVAILABLE = False

class ImprovedNERInference:
    """改进的NER推理类，包含后处理规则"""
    
    def __init__(self, model_path):
        self.base_inference = NERInference(model_path)
        
        # 置信度阈值设置
        self.confidence_thresholds = {
            'PER': 0.7,      # 人名
            'ORG': 0.6,      # 组织
            'LOC': 0.8,      # 地点  
            'TIME': 0.5,     # 时间
            'MONEY': 0.8,    # 金钱
            'PRODUCT': 0.7,  # 产品
            'EVENT': 0.6     # 事件
        }
        
        # 常见错误修正规则
        self.correction_rules = {
            # 产品名称规则
            'product_patterns': [
                r'iPhone\d*', r'iPad', r'MacBook', r'特斯拉', r'华为.*?手机',
                r'小米.*?手机', r'OPPO', r'vivo', r'抖音', r'微信', r'QQ'
            ],
            # 组织机构规则  
            'org_patterns': [
                r'.*?大学', r'.*?公司', r'.*?集团', r'.*?银行', r'.*?部门',
                r'腾讯', r'阿里巴巴', r'百度', r'字节跳动', r'华为', r'小米'
            ],
            # 地点规则
            'loc_patterns': [
                r'北京', r'上海', r'广州', r'深圳', r'杭州', r'南京', r'苏州',
                r'.*?市', r'.*?省', r'.*?县', r'.*?区'
            ],
            # 时间规则
            'time_patterns': [
                r'\d{4}年', r'明天', r'昨天', r'今天', r'明年', r'去年', r'今年',
                r'春季', r'夏季', r'秋季', r'冬季', r'上半年', r'下半年'
            ],
            # 金钱规则
            'money_patterns': [
                r'\d+元', r'\d+万元', r'\d+亿元', r'\d+美元', r'\d+万美元', r'\d+亿美元'
            ],
            # 事件规则
            'event_patterns': [
                r'春节', r'中秋节', r'国庆节', r'奥运会', r'世界杯', r'会议', r'研讨会',
                r'.*?节', r'.*?会', r'.*?典礼'
            ]
        }
    
    def predict_with_improvements(self, text: str):
        """使用改进规则进行预测"""
        # 获取基础预测结果
        base_result = self.base_inference.predict(text)
        
        if not base_result:
            return base_result
        
        # 应用改进规则
        improved_result = self.apply_improvements(base_result)
        
        return improved_result
    
    def apply_improvements(self, result):
        """应用改进规则"""
        chars = result.get('chars', [])
        labels = result.get('labels', [])
        confidence = result.get('confidence', [])
        
        # 1. 置信度过滤
        filtered_labels = self.filter_by_confidence(labels, confidence)
        
        # 2. 规则修正
        corrected_labels = self.apply_correction_rules(''.join(chars), filtered_labels)
        
        # 3. 边界修正
        final_labels = self.fix_entity_boundaries(chars, corrected_labels)
        
        # 重新提取实体
        entities = self.extract_entities_improved(chars, final_labels, confidence)
        
        # 更新结果
        improved_result = result.copy()
        improved_result['labels'] = final_labels
        improved_result['entities'] = entities
        improved_result['improvements_applied'] = True
        
        return improved_result
    
    def filter_by_confidence(self, labels, confidence):
        """根据置信度过滤标签"""
        filtered_labels = []
        
        for i, (label, conf) in enumerate(zip(labels, confidence)):
            if label == 'O':
                filtered_labels.append(label)
                continue
                
            # 提取实体类型
            if label.startswith('B-') or label.startswith('I-'):
                entity_type = label[2:]
                threshold = self.confidence_thresholds.get(entity_type, 0.5)
                
                if conf >= threshold:
                    filtered_labels.append(label)
                else:
                    filtered_labels.append('O')  # 置信度不够，标记为O
            else:
                filtered_labels.append(label)
        
        return filtered_labels
    
    def apply_correction_rules(self, text, labels):
        """应用规则修正"""
        corrected_labels = labels.copy()
        
        # 对每种实体类型应用规则
        for entity_type, patterns in self.correction_rules.items():
            if entity_type.endswith('_patterns'):
                target_type = entity_type.replace('_patterns', '').upper()
                
                for pattern in patterns:
                    for match in re.finditer(pattern, text):
                        start, end = match.span()
                        
                        # 检查是否应该更新标签
                        if self.should_apply_correction(corrected_labels[start:end], target_type):
                            # 更新标签
                            if end > start:
                                corrected_labels[start] = f'B-{target_type}'
                                for j in range(start + 1, end):
                                    if j < len(corrected_labels):
                                        corrected_labels[j] = f'I-{target_type}'
        
        return corrected_labels
    
    def should_apply_correction(self, current_labels, target_type):
        """判断是否应该应用修正"""
        # 如果当前没有标注或者置信度很低，则应用修正
        non_o_labels = [label for label in current_labels if label != 'O']
        
        if not non_o_labels:
            return True
            
        # 如果已经有相同类型的标注，不修正
        for label in non_o_labels:
            if label.endswith(target_type):
                return False
                
        return True
    
    def fix_entity_boundaries(self, chars, labels):
        """修正实体边界"""
        fixed_labels = labels.copy()
        
        i = 0
        while i < len(fixed_labels):
            label = fixed_labels[i]
            
            if label.startswith('I-'):
                # I-标签前面应该有对应的B-标签
                entity_type = label[2:]
                
                # 查找前面的标签
                if i == 0 or not fixed_labels[i-1].endswith(f'-{entity_type}'):
                    # 修正为B-标签
                    fixed_labels[i] = f'B-{entity_type}'
            
            elif label.startswith('B-'):
                # 确保B-标签后面的I-标签是连续的
                entity_type = label[2:]
                j = i + 1
                
                while j < len(fixed_labels) and fixed_labels[j] == f'I-{entity_type}':
                    j += 1
                
                # 检查是否有跳跃的I-标签
                while j < len(fixed_labels):
                    if fixed_labels[j] == f'I-{entity_type}':
                        # 有跳跃的I-标签，修正为B-标签
                        fixed_labels[j] = f'B-{entity_type}'
                        break
                    elif not fixed_labels[j] == 'O':
                        break
                    j += 1
            
            i += 1
        
        return fixed_labels
    
    def extract_entities_improved(self, chars, labels, confidence):
        """改进的实体提取"""
        entities = []
        current_entity = ""
        current_type = ""
        start_pos = -1
        entity_confidence = []
        
        for i, (char, label, conf) in enumerate(zip(chars, labels, confidence)):
            if label.startswith('B-'):
                # 结束之前的实体
                if current_entity:
                    avg_conf = sum(entity_confidence) / len(entity_confidence)
                    entities.append({
                        'entity': current_entity,
                        'type': current_type,
                        'start': start_pos,
                        'end': i - 1,
                        'confidence': avg_conf,
                        'improved': True
                    })
                
                # 开始新实体
                current_entity = char
                current_type = label[2:]
                start_pos = i
                entity_confidence = [conf]
                
            elif label.startswith('I-') and current_type == label[2:]:
                # 继续当前实体
                current_entity += char
                entity_confidence.append(conf)
                
            else:
                # 结束当前实体
                if current_entity:
                    avg_conf = sum(entity_confidence) / len(entity_confidence)
                    entities.append({
                        'entity': current_entity,
                        'type': current_type,
                        'start': start_pos,
                        'end': i - 1,
                        'confidence': avg_conf,
                        'improved': True
                    })
                    current_entity = ""
                    current_type = ""
                    entity_confidence = []
        
        # 处理最后的实体
        if current_entity:
            avg_conf = sum(entity_confidence) / len(entity_confidence)
            entities.append({
                'entity': current_entity,
                'type': current_type,
                'start': start_pos,
                'end': len(chars) - 1,
                'confidence': avg_conf,
                'improved': True
            })
        
        return entities

def test_improved_inference():
    """测试改进的推理效果"""
    if not MODEL_AVAILABLE:
        print("❌ 模型不可用")
        return
    
    # 检查模型路径
    best_model_path = os.path.join('model', 'best_model')
    chinese_model_path = os.path.join('model', 'chinese_ner_model')
    
    model_path = None
    if os.path.exists(best_model_path):
        model_path = best_model_path
    elif os.path.exists(chinese_model_path):
        model_path = chinese_model_path
    else:
        print("❌ 找不到模型文件")
        return
    
    print(f"🚀 测试改进的推理效果")
    print("=" * 60)
    
    # 创建改进的推理器
    improved_inference = ImprovedNERInference(model_path)
    
    # 测试样本
    test_samples = [
        "华为手机在2023年发布新产品",
        "清华大学举办人工智能研讨会", 
        "上海是一个国际化大都市",
        "疫情期间很多人在家办公",
        "苹果公司发布了新款iPhone15",
        "小明花了5000元买了一台电脑"
    ]
    
    for i, text in enumerate(test_samples, 1):
        print(f"\n📝 样本 {i}: {text}")
        
        # 原始预测
        original_result = improved_inference.base_inference.predict(text)
        original_entities = original_result.get('entities', [])
        
        # 改进预测
        improved_result = improved_inference.predict_with_improvements(text)
        improved_entities = improved_result.get('entities', [])
        
        print(f"🔧 原始识别 ({len(original_entities)} 个实体):")
        for entity in original_entities:
            print(f"   • {entity.get('entity', '')} ({entity.get('type', '')}) [置信度: {entity.get('confidence', 0):.3f}]")
        
        print(f"✨ 改进识别 ({len(improved_entities)} 个实体):")
        for entity in improved_entities:
            print(f"   • {entity.get('entity', '')} ({entity.get('type', '')}) [置信度: {entity.get('confidence', 0):.3f}]")
        
        # 对比差异
        if len(original_entities) != len(improved_entities):
            print(f"📊 实体数量变化: {len(original_entities)} → {len(improved_entities)}")

def create_improved_ui_integration():
    """创建UI集成代码"""
    integration_code = '''
# 在 ner_annotation_ui_with_model.py 中的改进集成代码

def init_improved_model(self):
    """初始化改进的NER模型"""
    if not MODEL_AVAILABLE:
        return
        
    try:
        # 使用改进的推理器
        from improve_annotation_quality import ImprovedNERInference
        
        best_model_path = os.path.join(os.path.dirname(__file__), 'model', 'best_model')
        chinese_model_path = os.path.join(os.path.dirname(__file__), 'model', 'chinese_ner_model')
        
        model_path = None
        if os.path.exists(best_model_path):
            model_path = best_model_path
        elif os.path.exists(chinese_model_path):
            model_path = chinese_model_path
        
        if model_path:
            self.improved_inference = ImprovedNERInference(model_path)
            self.model_loaded = True
            print(f"✅ 改进模型加载成功: {model_path}")
        else:
            print("⚠️  未找到训练好的模型文件")
            
    except Exception as e:
        print(f"❌ 改进模型加载失败: {e}")
        self.model_loaded = False

def predict_entities_improved(self, text: str):
    """使用改进的模型预测实体"""
    if not self.model_loaded or not hasattr(self, 'improved_inference'):
        return None
        
    try:
        # 使用改进的推理器
        result = self.improved_inference.predict_with_improvements(text)
        return result
        
    except Exception as e:
        print(f"改进预测失败: {e}")
        return None
'''
    
    with open('ui_integration_improvements.py', 'w', encoding='utf-8') as f:
        f.write(integration_code)
    
    print("📄 UI集成代码已保存到: ui_integration_improvements.py")

def main():
    """主函数"""
    print("🔧 智能标注质量改进工具")
    print("=" * 50)
    
    # 测试改进效果
    test_improved_inference()
    
    # 创建UI集成代码
    create_improved_ui_integration()
    
    print("""
🎯 改进方案总结:
===============

✅ 已实现的改进:
1. 置信度阈值过滤 - 过滤低置信度预测
2. 规则修正 - 基于正则表达式的实体类型修正
3. 边界修正 - 修复BIO标签的一致性问题
4. 实体后处理 - 改进实体提取逻辑

🔧 使用方法:
1. 运行此脚本查看改进效果
2. 将改进的推理器集成到UI中
3. 调整置信度阈值以平衡准确率和召回率
4. 根据实际效果调整规则

💡 进一步优化建议:
1. 收集更多高质量标注数据
2. 使用领域特定的预训练模型
3. 实现多模型集成
4. 添加用户反馈学习机制
""")

if __name__ == "__main__":
    main()