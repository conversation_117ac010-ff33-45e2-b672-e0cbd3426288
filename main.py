#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NER数据生成器主程序
"""

from src.ner_generator import NERGenerator
from src.config import DATASET_CONFIG
from utils.validator import DataValidator

def main():
    """主函数 - 生成20000条NER训练数据"""
    print("NER数据生成器")
    print("=" * 50)
    
    # 创建生成器
    generator = NERGenerator(DATASET_CONFIG)
    
    # 生成数据集
    dataset = generator.generate_dataset()
    
    # 分析数据集
    generator.analyze_dataset(dataset)
    
    # 保存数据集
    generator.save_dataset(dataset)
    
    # 验证数据质量
    print("\n验证数据质量...")
    import glob
    json_files = glob.glob("data/ner_dataset_*.json")
    if json_files:
        latest_file = max(json_files)
        DataValidator.validate_dataset(latest_file)
    
    print("\n数据生成完成！")
    print("生成的文件位于 data/ 目录下")

if __name__ == "__main__":
    main()
