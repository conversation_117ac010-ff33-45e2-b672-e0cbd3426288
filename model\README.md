# NER模型训练模块

基于BERT的中文命名实体识别模型训练和推理模块。

## 目录结构

```
model/
├── train_ner_model.py      # 核心训练脚本
├── data_processor.py       # 数据预处理模块
├── evaluate_model.py       # 模型评估脚本
├── inference.py            # 模型推理脚本
├── config.py              # 配置文件
├── start_training.py       # 一键训练启动脚本
├── requirements.txt        # 依赖包列表
├── README.md              # 本文件
├── models/                # 训练过程中的模型保存
├── output/                # 输出文件目录
├── logs/                  # 日志文件目录
├── cache/                 # 缓存目录
└── best_model/            # 最佳模型保存目录
```

## 快速开始

### 1. 安装依赖

```bash
cd model
pip install -r requirements.txt
```

### 2. 一键训练

```bash
python start_training.py
```

这将自动完成：
- 依赖检查
- 数据预处理
- 模型训练
- 模型评估

### 3. 使用训练好的模型

```bash
# 交互式预测
python inference.py --interactive

# 预测单个文本
python inference.py --text "张三在北京大学工作"

# 批量预测文件
python inference.py --input_file input.txt --output_file output.txt
```

## 详细使用说明

### 数据预处理

```bash
python data_processor.py
```

功能：
- 加载JSON/CoNLL/BIO格式数据
- 数据质量验证
- 数据集分割（训练/验证/测试）
- 数据统计分析

### 模型训练

```bash
python train_ner_model.py
```

配置选项：
- 模型类型：bert-base-chinese（默认）
- 训练轮数：5轮
- 批次大小：16
- 学习率：2e-5
- 最大序列长度：128

### 模型评估

```bash
python evaluate_model.py
```

评估指标：
- 序列级别：准确率、精确率、召回率、F1分数
- 实体级别：精确率、召回率、F1分数
- 按实体类型统计
- 详细分类报告

### 模型推理

```bash
# 基本用法
python inference.py --text "要预测的文本"

# 详细输出
python inference.py --text "要预测的文本" --format detailed

# JSON格式输出
python inference.py --text "要预测的文本" --format json

# 交互式模式
python inference.py --interactive

# 批量处理
python inference.py --input_file texts.txt --output_file results.txt
```

## 配置说明

### 模型配置 (config.py)

```python
MODEL_CONFIG = {
    'model_name': 'bert-base-chinese',  # 预训练模型
    'max_length': 128,                  # 最大序列长度
    'epochs': 5,                        # 训练轮数
    'batch_size': 16,                   # 批次大小
    'learning_rate': 2e-5,              # 学习率
}
```

### 支持的预训练模型

- `bert-base-chinese`: Google官方中文BERT
- `hfl/chinese-bert-wwm-ext`: 哈工大中文BERT-wwm
- `hfl/chinese-roberta-wwm-ext`: 哈工大中文RoBERTa
- `hfl/chinese-electra-180g-small-discriminator`: 哈工大中文ELECTRA

### 实体类型

支持7种实体类型：
- PER: 人名
- ORG: 组织机构
- LOC: 地点
- TIME: 时间
- MONEY: 金额
- PRODUCT: 产品
- EVENT: 事件

## 训练流程

### 1. 数据准备
- 加载生成的NER数据集
- 验证BIO格式正确性
- 分割训练/验证/测试集

### 2. 模型训练
- 初始化BERT模型
- 设置优化器和学习率调度
- 训练指定轮数
- 保存最佳模型

### 3. 模型评估
- 在测试集上评估性能
- 生成详细评估报告
- 计算各种指标

### 4. 模型部署
- 保存完整模型
- 提供推理接口
- 支持批量预测

## 性能优化

### GPU加速
```python
# 自动检测GPU
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
```

### 内存优化
- 使用梯度累积
- 动态批次大小
- 模型并行化

### 训练技巧
- 学习率预热
- 权重衰减
- 早停机制

## 常见问题

### Q: 训练时显存不足怎么办？
A: 减小batch_size或max_length，或使用梯度累积。

### Q: 如何提高模型性能？
A: 
1. 增加训练数据
2. 调整超参数
3. 使用更大的预训练模型
4. 数据增强

### Q: 如何处理新的实体类型？
A: 修改config.py中的LABEL_CONFIG，重新训练模型。

### Q: 模型预测速度慢怎么办？
A: 
1. 使用GPU推理
2. 模型量化
3. 批量预测
4. 使用更小的模型

## 输出文件说明

### 训练输出
- `best_model/`: 最佳模型文件
- `chinese_ner_model/`: 最终模型文件
- `output/train_data.json`: 训练数据
- `output/val_data.json`: 验证数据
- `output/test_data.json`: 测试数据

### 评估输出
- `output/evaluation_report.json`: 详细评估报告
- `training.log`: 训练日志

### 推理输出
- 支持多种格式：simple、detailed、json
- 包含实体识别结果和置信度

## 扩展功能

### 自定义数据格式
继承NERDataProcessor类，实现自定义数据加载器。

### 自定义模型
继承NERTrainer类，实现自定义训练逻辑。

### 自定义评估指标
修改evaluate_model.py，添加新的评估指标。

## 技术栈

- **深度学习框架**: PyTorch
- **预训练模型**: Transformers (Hugging Face)
- **数据处理**: NumPy, Pandas
- **评估指标**: scikit-learn, seqeval
- **可视化**: Matplotlib, Seaborn

## 许可证

MIT License
