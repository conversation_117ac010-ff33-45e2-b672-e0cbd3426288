{"architectures": ["BertForTokenClassification"], "attention_probs_dropout_prob": 0.1, "classifier_dropout": null, "directionality": "bidi", "hidden_act": "gelu", "hidden_dropout_prob": 0.1, "hidden_size": 768, "id2label": {"0": "O", "1": "B-PER", "2": "I-PER", "3": "B-ORG", "4": "I-ORG", "5": "B-LOC", "6": "I-LOC", "7": "B-TIME", "8": "I-TIME", "9": "B-MONEY", "10": "I-MONEY", "11": "B-PRODUCT", "12": "I-PRODUCT", "13": "B-EVENT", "14": "I-EVENT"}, "initializer_range": 0.02, "intermediate_size": 3072, "label2id": {"B-EVENT": 13, "B-LOC": 5, "B-MONEY": 9, "B-ORG": 3, "B-PER": 1, "B-PRODUCT": 11, "B-TIME": 7, "I-EVENT": 14, "I-LOC": 6, "I-MONEY": 10, "I-ORG": 4, "I-PER": 2, "I-PRODUCT": 12, "I-TIME": 8, "O": 0}, "layer_norm_eps": 1e-12, "max_position_embeddings": 512, "model_type": "bert", "num_attention_heads": 12, "num_hidden_layers": 12, "pad_token_id": 0, "pooler_fc_size": 768, "pooler_num_attention_heads": 12, "pooler_num_fc_layers": 3, "pooler_size_per_head": 128, "pooler_type": "first_token_transform", "position_embedding_type": "absolute", "torch_dtype": "float32", "transformers_version": "4.53.2", "type_vocab_size": 2, "use_cache": true, "vocab_size": 21128}