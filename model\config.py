#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NER模型训练配置文件
"""

import os

# 模型配置
MODEL_CONFIG = {
    # 基础模型
    'model_name': 'bert-base-chinese',  # 可选: 'hfl/chinese-bert-wwm-ext', 'hfl/chinese-roberta-wwm-ext'
    'max_length': 128,                  # 最大序列长度
    'num_labels': 15,                   # 标签数量
    
    # 训练参数
    'epochs': 5,                        # 训练轮数
    'batch_size': 16,                   # 批次大小
    'learning_rate': 2e-5,              # 学习率
    'weight_decay': 0.01,               # 权重衰减
    'warmup_steps': 0,                  # 预热步数
    
    # 数据分割
    'train_ratio': 0.8,                 # 训练集比例
    'val_ratio': 0.1,                   # 验证集比例
    'test_ratio': 0.1,                  # 测试集比例
    
    # 保存设置
    'save_steps': 500,                  # 保存间隔
    'eval_steps': 100,                  # 评估间隔
    'logging_steps': 50,                # 日志间隔
    'save_total_limit': 3,              # 最多保存模型数
}

# 标签配置
LABEL_CONFIG = {
    'labels': [
        'O',                            # 非实体
        'B-PER', 'I-PER',             # 人名
        'B-ORG', 'I-ORG',             # 组织
        'B-LOC', 'I-LOC',             # 地点
        'B-TIME', 'I-TIME',           # 时间
        'B-MONEY', 'I-MONEY',         # 金额
        'B-PRODUCT', 'I-PRODUCT',     # 产品
        'B-EVENT', 'I-EVENT'          # 事件
    ],
    
    'entity_types': {
        'PER': '人名',
        'ORG': '组织机构',
        'LOC': '地点',
        'TIME': '时间',
        'MONEY': '金额',
        'PRODUCT': '产品',
        'EVENT': '事件'
    }
}

# 路径配置
PATH_CONFIG = {
    'data_dir': '../data',
    'model_dir': './models',
    'output_dir': './output',
    'log_dir': './logs',
    'cache_dir': './cache'
}

# 数据文件配置
DATA_CONFIG = {
    'train_file': 'ner_dataset_20250803_153737.json',
    'conll_file': 'ner_dataset_20250803_153737.conll',
    'bio_file': 'ner_dataset_20250803_153737.bio'
}

# 评估配置
EVAL_CONFIG = {
    'metrics': ['precision', 'recall', 'f1'],
    'average': 'weighted',              # 平均方式
    'return_entity_level_metrics': True # 返回实体级别指标
}

# GPU配置
DEVICE_CONFIG = {
    'use_cuda': True,                   # 是否使用CUDA
    'cuda_device': 0,                   # CUDA设备ID
    'fp16': False,                      # 是否使用半精度
    'dataloader_num_workers': 4         # 数据加载器工作进程数
}

# 获取完整配置
def get_config():
    """获取完整配置"""
    config = {
        'model': MODEL_CONFIG,
        'labels': LABEL_CONFIG,
        'paths': PATH_CONFIG,
        'data': DATA_CONFIG,
        'eval': EVAL_CONFIG,
        'device': DEVICE_CONFIG
    }
    
    # 创建必要的目录
    for path in PATH_CONFIG.values():
        os.makedirs(path, exist_ok=True)
    
    return config

# 预定义的模型配置
PRETRAINED_MODELS = {
    'bert-base-chinese': {
        'name': 'bert-base-chinese',
        'description': 'Google官方中文BERT基础模型',
        'max_length': 512,
        'vocab_size': 21128
    },
    
    'chinese-bert-wwm-ext': {
        'name': 'hfl/chinese-bert-wwm-ext',
        'description': '哈工大中文BERT-wwm扩展模型',
        'max_length': 512,
        'vocab_size': 21128
    },
    
    'chinese-roberta-wwm-ext': {
        'name': 'hfl/chinese-roberta-wwm-ext',
        'description': '哈工大中文RoBERTa-wwm扩展模型',
        'max_length': 512,
        'vocab_size': 21128
    },
    
    'chinese-electra-small': {
        'name': 'hfl/chinese-electra-180g-small-discriminator',
        'description': '哈工大中文ELECTRA小模型',
        'max_length': 512,
        'vocab_size': 21128
    }
}

def get_model_config(model_name):
    """获取指定模型的配置"""
    if model_name in PRETRAINED_MODELS:
        return PRETRAINED_MODELS[model_name]
    else:
        return PRETRAINED_MODELS['bert-base-chinese']  # 默认模型
