#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NER数据预处理模块
支持多种数据格式的加载和转换
"""

import json
import os
from typing import List, Dict, Tuple
import random
from collections import Counter
import logging

logger = logging.getLogger(__name__)

class NERDataProcessor:
    """NER数据预处理器"""
    
    def __init__(self, config=None):
        self.config = config or {}
        self.labels = [
            'O', 'B-PER', 'I-PER', 'B-ORG', 'I-ORG', 'B-LOC', 'I-LOC',
            'B-TIME', 'I-TIME', 'B-MONEY', 'I-MONEY', 'B-PRODUCT', 'I-PRODUCT',
            'B-EVENT', 'I-EVENT'
        ]
        self.label2id = {label: i for i, label in enumerate(self.labels)}
        self.id2label = {i: label for i, label in enumerate(self.labels)}
    
    def load_json_data(self, file_path: str) -> List[Dict]:
        """加载JSON格式数据"""
        logger.info(f"加载JSON数据: {file_path}")
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        logger.info(f"加载了 {len(data)} 条数据")
        return data
    
    def load_conll_data(self, file_path: str) -> List[Dict]:
        """加载CoNLL格式数据"""
        logger.info(f"加载CoNLL数据: {file_path}")
        data = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        current_chars = []
        current_tags = []
        sample_id = 1
        
        for line in lines:
            line = line.strip()
            
            if line.startswith('#'):
                # 注释行，提取句子信息
                if 'Sentence' in line and ':' in line:
                    sentence = line.split(':', 1)[1].strip()
                continue
            
            if not line:
                # 空行，表示一个样本结束
                if current_chars and current_tags:
                    data.append({
                        'id': sample_id,
                        'sentence': ''.join(current_chars),
                        'chars': current_chars.copy(),
                        'tags': current_tags.copy(),
                        'length': len(current_chars)
                    })
                    sample_id += 1
                    current_chars.clear()
                    current_tags.clear()
            else:
                # 数据行
                parts = line.split('\t')
                if len(parts) >= 2:
                    char, tag = parts[0], parts[1]
                    current_chars.append(char)
                    current_tags.append(tag)
        
        # 处理最后一个样本
        if current_chars and current_tags:
            data.append({
                'id': sample_id,
                'sentence': ''.join(current_chars),
                'chars': current_chars.copy(),
                'tags': current_tags.copy(),
                'length': len(current_chars)
            })
        
        logger.info(f"加载了 {len(data)} 条数据")
        return data
    
    def load_bio_data(self, file_path: str) -> List[Dict]:
        """加载BIO格式数据"""
        logger.info(f"加载BIO数据: {file_path}")
        data = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        # 按双换行分割样本
        samples = content.split('\n\n')
        
        for i, sample in enumerate(samples):
            lines = sample.strip().split('\n')
            if len(lines) >= 2:
                chars_line = lines[0]
                tags_line = lines[1]
                
                chars = chars_line.split()
                tags = tags_line.split()
                
                if len(chars) == len(tags):
                    data.append({
                        'id': i + 1,
                        'sentence': ''.join(chars),
                        'chars': chars,
                        'tags': tags,
                        'length': len(chars)
                    })
        
        logger.info(f"加载了 {len(data)} 条数据")
        return data
    
    def validate_data(self, data: List[Dict]) -> Tuple[List[Dict], List[str]]:
        """验证数据质量"""
        logger.info("验证数据质量...")
        valid_data = []
        errors = []
        
        for sample in data:
            sample_id = sample['id']
            chars = sample['chars']
            tags = sample['tags']
            
            # 检查基本格式
            if len(chars) != len(tags):
                errors.append(f"样本{sample_id}: 字符数与标签数不匹配")
                continue
            
            # 检查标签有效性
            invalid_tags = [tag for tag in tags if tag not in self.labels]
            if invalid_tags:
                errors.append(f"样本{sample_id}: 无效标签 {invalid_tags}")
                continue
            
            # 检查BIO格式
            bio_error = self._check_bio_format(tags, sample_id)
            if bio_error:
                errors.append(bio_error)
                continue
            
            valid_data.append(sample)
        
        logger.info(f"验证完成: {len(valid_data)} 条有效数据, {len(errors)} 个错误")
        return valid_data, errors
    
    def _check_bio_format(self, tags: List[str], sample_id: int) -> str:
        """检查BIO格式正确性"""
        for i, tag in enumerate(tags):
            if tag.startswith('I-'):
                if i == 0:
                    return f"样本{sample_id}: I标签不能出现在句子开头"
                
                prev_tag = tags[i-1]
                entity_type = tag[2:]
                
                if prev_tag not in [f'B-{entity_type}', f'I-{entity_type}']:
                    return f"样本{sample_id}: I标签前置条件错误"
        
        return None
    
    def split_data(self, data: List[Dict], train_ratio=0.8, val_ratio=0.1, test_ratio=0.1, 
                   random_seed=42) -> Tuple[List[Dict], List[Dict], List[Dict]]:
        """分割数据集"""
        assert abs(train_ratio + val_ratio + test_ratio - 1.0) < 1e-6, "比例之和必须为1"
        
        # 设置随机种子
        random.seed(random_seed)
        data_copy = data.copy()
        random.shuffle(data_copy)
        
        total_size = len(data_copy)
        train_size = int(total_size * train_ratio)
        val_size = int(total_size * val_ratio)
        
        train_data = data_copy[:train_size]
        val_data = data_copy[train_size:train_size + val_size]
        test_data = data_copy[train_size + val_size:]
        
        logger.info(f"数据分割完成:")
        logger.info(f"  训练集: {len(train_data)} 条 ({len(train_data)/total_size:.1%})")
        logger.info(f"  验证集: {len(val_data)} 条 ({len(val_data)/total_size:.1%})")
        logger.info(f"  测试集: {len(test_data)} 条 ({len(test_data)/total_size:.1%})")
        
        return train_data, val_data, test_data
    
    def analyze_data(self, data: List[Dict]) -> Dict:
        """分析数据统计信息"""
        logger.info("分析数据统计信息...")
        
        # 基本统计
        total_samples = len(data)
        total_chars = sum(sample['length'] for sample in data)
        avg_length = total_chars / total_samples if total_samples > 0 else 0
        
        lengths = [sample['length'] for sample in data]
        min_length = min(lengths) if lengths else 0
        max_length = max(lengths) if lengths else 0
        
        # 标签统计
        label_counts = Counter()
        entity_counts = Counter()
        
        for sample in data:
            for tag in sample['tags']:
                label_counts[tag] += 1
                if tag != 'O':
                    entity_type = tag.split('-')[1]
                    entity_counts[entity_type] += 1
        
        # 实体长度统计
        entity_lengths = []
        for sample in data:
            current_entity_length = 0
            for tag in sample['tags']:
                if tag.startswith('B-'):
                    if current_entity_length > 0:
                        entity_lengths.append(current_entity_length)
                    current_entity_length = 1
                elif tag.startswith('I-'):
                    current_entity_length += 1
                else:
                    if current_entity_length > 0:
                        entity_lengths.append(current_entity_length)
                        current_entity_length = 0
            
            if current_entity_length > 0:
                entity_lengths.append(current_entity_length)
        
        avg_entity_length = sum(entity_lengths) / len(entity_lengths) if entity_lengths else 0
        
        stats = {
            'total_samples': total_samples,
            'total_chars': total_chars,
            'avg_length': avg_length,
            'min_length': min_length,
            'max_length': max_length,
            'label_counts': dict(label_counts),
            'entity_counts': dict(entity_counts),
            'avg_entity_length': avg_entity_length,
            'total_entities': len(entity_lengths)
        }
        
        return stats
    
    def save_processed_data(self, data: List[Dict], output_path: str, format_type='json'):
        """保存处理后的数据"""
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        if format_type == 'json':
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        
        elif format_type == 'conll':
            with open(output_path, 'w', encoding='utf-8') as f:
                for sample in data:
                    f.write(f"# Sentence {sample['id']}: {sample['sentence']}\n")
                    for char, tag in zip(sample['chars'], sample['tags']):
                        f.write(f"{char}\t{tag}\n")
                    f.write("\n")
        
        elif format_type == 'bio':
            with open(output_path, 'w', encoding='utf-8') as f:
                for sample in data:
                    chars_line = " ".join(sample['chars'])
                    tags_line = " ".join(sample['tags'])
                    f.write(f"{chars_line}\n{tags_line}\n\n")
        
        logger.info(f"数据已保存到: {output_path}")


def main():
    """数据预处理主函数"""
    processor = NERDataProcessor()
    
    # 加载数据
    data_path = "../data/ner_dataset_20250803_153737.json"
    data = processor.load_json_data(data_path)
    
    # 验证数据
    valid_data, errors = processor.validate_data(data)
    
    if errors:
        print("发现数据错误:")
        for error in errors[:10]:
            print(f"  {error}")
    
    # 分析数据
    stats = processor.analyze_data(valid_data)
    print("\n数据统计信息:")
    print(f"  总样本数: {stats['total_samples']}")
    print(f"  平均长度: {stats['avg_length']:.2f}")
    print(f"  实体数量: {stats['total_entities']}")
    print(f"  平均实体长度: {stats['avg_entity_length']:.2f}")
    
    # 分割数据
    train_data, val_data, test_data = processor.split_data(valid_data)
    
    # 保存分割后的数据
    processor.save_processed_data(train_data, "./output/train_data.json")
    processor.save_processed_data(val_data, "./output/val_data.json")
    processor.save_processed_data(test_data, "./output/test_data.json")


if __name__ == "__main__":
    main()
