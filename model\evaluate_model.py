#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NER模型评估脚本
提供详细的模型性能评估和分析
"""

import torch
import json
import numpy as np
from transformers import BertTokenizer, BertForTokenClassification
from sklearn.metrics import classification_report, confusion_matrix
from seqeval.metrics import accuracy_score, f1_score, precision_score, recall_score
from seqeval.metrics import classification_report as seq_classification_report
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict
import logging

logger = logging.getLogger(__name__)

class NERModelEvaluator:
    """NER模型评估器"""
    
    def __init__(self, model_path, device=None):
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model_path = model_path
        
        # 加载模型和分词器
        self.model = BertForTokenClassification.from_pretrained(model_path)
        self.tokenizer = BertTokenizer.from_pretrained(model_path)
        self.model.to(self.device)
        self.model.eval()
        
        # 加载标签映射
        with open(f"{model_path}/label_mapping.json", 'r', encoding='utf-8') as f:
            mapping = json.load(f)
            self.label2id = mapping['label2id']
            self.id2label = {int(k): v for k, v in mapping['id2label'].items()}
            self.labels = mapping['labels']
    
    def predict_single(self, chars, max_length=128):
        """预测单个样本"""
        # 编码输入
        encoding = self.tokenizer(
            chars,
            is_split_into_words=True,
            padding='max_length',
            truncation=True,
            max_length=max_length,
            return_tensors='pt'
        )
        
        input_ids = encoding['input_ids'].to(self.device)
        attention_mask = encoding['attention_mask'].to(self.device)
        
        with torch.no_grad():
            outputs = self.model(input_ids=input_ids, attention_mask=attention_mask)
            predictions = torch.argmax(outputs.logits, dim=-1)
        
        # 解码预测结果
        word_ids = encoding.word_ids()
        predicted_labels = []
        
        for i, word_idx in enumerate(word_ids):
            if word_idx is not None and i < len(predictions[0]):
                predicted_labels.append(self.id2label[predictions[0][i].item()])
        
        # 确保预测标签数量与输入字符数量一致
        predicted_labels = predicted_labels[:len(chars)]
        while len(predicted_labels) < len(chars):
            predicted_labels.append('O')
        
        return predicted_labels
    
    def evaluate_dataset(self, test_data):
        """评估整个数据集"""
        logger.info(f"评估 {len(test_data)} 个样本...")
        
        all_true_labels = []
        all_pred_labels = []
        all_true_entities = []
        all_pred_entities = []
        
        for sample in test_data:
            chars = sample['chars']
            true_tags = sample['tags']
            
            # 预测
            pred_tags = self.predict_single(chars)
            
            all_true_labels.append(true_tags)
            all_pred_labels.append(pred_tags)
            
            # 提取实体
            true_entities = self.extract_entities(chars, true_tags)
            pred_entities = self.extract_entities(chars, pred_tags)
            
            all_true_entities.extend(true_entities)
            all_pred_entities.extend(pred_entities)
        
        # 计算指标
        results = self.calculate_metrics(all_true_labels, all_pred_labels, 
                                       all_true_entities, all_pred_entities)
        
        return results
    
    def extract_entities(self, chars, tags):
        """从标签序列中提取实体"""
        entities = []
        current_entity = ""
        current_type = ""
        start_pos = -1
        
        for i, (char, tag) in enumerate(zip(chars, tags)):
            if tag.startswith('B-'):
                if current_entity:
                    entities.append((current_entity, current_type, start_pos, i-1))
                current_entity = char
                current_type = tag[2:]
                start_pos = i
            elif tag.startswith('I-') and current_entity and tag[2:] == current_type:
                current_entity += char
            else:
                if current_entity:
                    entities.append((current_entity, current_type, start_pos, i-1))
                    current_entity = ""
                    current_type = ""
                    start_pos = -1
        
        if current_entity:
            entities.append((current_entity, current_type, start_pos, len(chars)-1))
        
        return entities
    
    def calculate_metrics(self, true_labels, pred_labels, true_entities, pred_entities):
        """计算各种评估指标"""
        results = {}
        
        # 序列级别指标
        results['sequence_accuracy'] = accuracy_score(true_labels, pred_labels)
        results['sequence_f1'] = f1_score(true_labels, pred_labels)
        results['sequence_precision'] = precision_score(true_labels, pred_labels)
        results['sequence_recall'] = recall_score(true_labels, pred_labels)
        
        # 实体级别指标
        true_entity_set = set((entity[0], entity[1]) for entity in true_entities)
        pred_entity_set = set((entity[0], entity[1]) for entity in pred_entities)
        
        if len(true_entity_set) > 0:
            entity_precision = len(true_entity_set & pred_entity_set) / len(pred_entity_set) if pred_entity_set else 0
            entity_recall = len(true_entity_set & pred_entity_set) / len(true_entity_set)
            entity_f1 = 2 * entity_precision * entity_recall / (entity_precision + entity_recall) if (entity_precision + entity_recall) > 0 else 0
        else:
            entity_precision = entity_recall = entity_f1 = 0
        
        results['entity_precision'] = entity_precision
        results['entity_recall'] = entity_recall
        results['entity_f1'] = entity_f1
        
        # 按实体类型统计
        entity_type_stats = defaultdict(lambda: {'true': 0, 'pred': 0, 'correct': 0})
        
        for entity in true_entities:
            entity_type_stats[entity[1]]['true'] += 1
        
        for entity in pred_entities:
            entity_type_stats[entity[1]]['pred'] += 1
        
        for entity in true_entities:
            if entity in pred_entities:
                entity_type_stats[entity[1]]['correct'] += 1
        
        # 计算每个实体类型的指标
        type_metrics = {}
        for entity_type, stats in entity_type_stats.items():
            precision = stats['correct'] / stats['pred'] if stats['pred'] > 0 else 0
            recall = stats['correct'] / stats['true'] if stats['true'] > 0 else 0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            
            type_metrics[entity_type] = {
                'precision': precision,
                'recall': recall,
                'f1': f1,
                'support': stats['true']
            }
        
        results['entity_type_metrics'] = type_metrics
        
        # 详细分类报告
        flat_true = [tag for tags in true_labels for tag in tags]
        flat_pred = [tag for tags in pred_labels for tag in tags]
        
        results['classification_report'] = classification_report(
            flat_true, flat_pred, 
            target_names=self.labels,
            output_dict=True,
            zero_division=0
        )
        
        return results
    
    def print_evaluation_results(self, results):
        """打印评估结果"""
        print("\n" + "="*60)
        print("NER模型评估结果")
        print("="*60)
        
        print(f"\n序列级别指标:")
        print(f"  准确率: {results['sequence_accuracy']:.4f}")
        print(f"  精确率: {results['sequence_precision']:.4f}")
        print(f"  召回率: {results['sequence_recall']:.4f}")
        print(f"  F1分数: {results['sequence_f1']:.4f}")
        
        print(f"\n实体级别指标:")
        print(f"  精确率: {results['entity_precision']:.4f}")
        print(f"  召回率: {results['entity_recall']:.4f}")
        print(f"  F1分数: {results['entity_f1']:.4f}")
        
        print(f"\n各实体类型性能:")
        for entity_type, metrics in results['entity_type_metrics'].items():
            print(f"  {entity_type}:")
            print(f"    精确率: {metrics['precision']:.4f}")
            print(f"    召回率: {metrics['recall']:.4f}")
            print(f"    F1分数: {metrics['f1']:.4f}")
            print(f"    支持数: {metrics['support']}")
    
    def save_evaluation_report(self, results, output_path):
        """保存评估报告"""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        logger.info(f"评估报告已保存到: {output_path}")
    
    def demo_prediction(self, text):
        """演示预测功能"""
        chars = list(text)
        predicted_tags = self.predict_single(chars)
        entities = self.extract_entities(chars, predicted_tags)
        
        print(f"\n输入文本: {text}")
        print("预测结果:")
        
        # 显示字符级标注
        for char, tag in zip(chars, predicted_tags):
            if tag != 'O':
                print(f"  {char}: {tag}")
        
        # 显示实体
        print("\n识别的实体:")
        for entity, entity_type, start, end in entities:
            print(f"  {entity} -> {entity_type}")


def main():
    """主评估函数"""
    import sys
    
    # 检查模型路径
    model_path = "./best_model"
    if not os.path.exists(model_path):
        print(f"模型路径不存在: {model_path}")
        print("请先训练模型")
        return
    
    # 加载测试数据
    test_data_path = "./output/test_data.json"
    if not os.path.exists(test_data_path):
        print(f"测试数据不存在: {test_data_path}")
        print("请先运行数据预处理")
        return
    
    with open(test_data_path, 'r', encoding='utf-8') as f:
        test_data = json.load(f)
    
    # 创建评估器
    evaluator = NERModelEvaluator(model_path)
    
    # 评估模型
    results = evaluator.evaluate_dataset(test_data)
    
    # 打印结果
    evaluator.print_evaluation_results(results)
    
    # 保存报告
    evaluator.save_evaluation_report(results, "./output/evaluation_report.json")
    
    # 演示预测
    demo_texts = [
        "张三在北京大学工作",
        "李四于2024年购买了iPhone手机",
        "阿里巴巴公司投资100万元"
    ]
    
    print("\n" + "="*60)
    print("预测演示")
    print("="*60)
    
    for text in demo_texts:
        evaluator.demo_prediction(text)


if __name__ == "__main__":
    import os
    main()
