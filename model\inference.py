#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NER模型推理脚本
提供简单易用的模型推理接口
"""

import torch
import json
from transformers import BertTokenizer, BertForTokenClassification
from typing import List, Tuple, Dict
import argparse
import logging

logger = logging.getLogger(__name__)

class NERInference:
    """NER模型推理类"""
    
    def __init__(self, model_path: str, device=None):
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model_path = model_path
        
        # 加载模型和分词器
        logger.info(f"加载模型: {model_path}")
        self.model = BertForTokenClassification.from_pretrained(model_path)
        self.tokenizer = BertTokenizer.from_pretrained(model_path)
        self.model.to(self.device)
        self.model.eval()
        
        # 加载标签映射
        with open(f"{model_path}/label_mapping.json", 'r', encoding='utf-8') as f:
            mapping = json.load(f)
            self.label2id = mapping['label2id']
            self.id2label = {int(k): v for k, v in mapping['id2label'].items()}
            self.labels = mapping['labels']
        
        logger.info(f"模型加载完成，支持 {len(self.labels)} 种标签")
    
    def predict(self, text: str, max_length: int = 128) -> Dict:
        """预测文本的NER标签"""
        chars = list(text)
        
        # 编码输入
        encoding = self.tokenizer(
            chars,
            is_split_into_words=True,
            padding='max_length',
            truncation=True,
            max_length=max_length,
            return_tensors='pt'
        )
        
        input_ids = encoding['input_ids'].to(self.device)
        attention_mask = encoding['attention_mask'].to(self.device)
        
        # 预测
        with torch.no_grad():
            outputs = self.model(input_ids=input_ids, attention_mask=attention_mask)
            predictions = torch.argmax(outputs.logits, dim=-1)
            probabilities = torch.softmax(outputs.logits, dim=-1)
        
        # 解码预测结果
        word_ids = encoding.word_ids()
        predicted_labels = []
        confidence_scores = []
        
        for i, word_idx in enumerate(word_ids):
            if word_idx is not None and word_idx < len(chars):
                label_id = predictions[0][i].item()
                predicted_labels.append(self.id2label[label_id])
                confidence_scores.append(probabilities[0][i][label_id].item())
        
        # 确保预测标签数量与输入字符数量一致
        predicted_labels = predicted_labels[:len(chars)]
        confidence_scores = confidence_scores[:len(chars)]
        
        while len(predicted_labels) < len(chars):
            predicted_labels.append('O')
            confidence_scores.append(1.0)
        
        # 提取实体
        entities = self.extract_entities(chars, predicted_labels, confidence_scores)
        
        return {
            'text': text,
            'chars': chars,
            'labels': predicted_labels,
            'confidence': confidence_scores,
            'entities': entities
        }
    
    def extract_entities(self, chars: List[str], labels: List[str], 
                        confidence_scores: List[float]) -> List[Dict]:
        """从标签序列中提取实体"""
        entities = []
        current_entity = ""
        current_type = ""
        start_pos = -1
        entity_confidence = []
        
        for i, (char, label, conf) in enumerate(zip(chars, labels, confidence_scores)):
            if label.startswith('B-'):
                # 保存前一个实体
                if current_entity:
                    avg_confidence = sum(entity_confidence) / len(entity_confidence)
                    entities.append({
                        'text': current_entity,
                        'type': current_type,
                        'start': start_pos,
                        'end': i - 1,
                        'confidence': avg_confidence
                    })
                
                # 开始新实体
                current_entity = char
                current_type = label[2:]
                start_pos = i
                entity_confidence = [conf]
                
            elif label.startswith('I-') and current_entity and label[2:] == current_type:
                current_entity += char
                entity_confidence.append(conf)
                
            else:
                # 保存当前实体
                if current_entity:
                    avg_confidence = sum(entity_confidence) / len(entity_confidence)
                    entities.append({
                        'text': current_entity,
                        'type': current_type,
                        'start': start_pos,
                        'end': i - 1,
                        'confidence': avg_confidence
                    })
                    current_entity = ""
                    current_type = ""
                    entity_confidence = []
        
        # 处理最后一个实体
        if current_entity:
            avg_confidence = sum(entity_confidence) / len(entity_confidence)
            entities.append({
                'text': current_entity,
                'type': current_type,
                'start': start_pos,
                'end': len(chars) - 1,
                'confidence': avg_confidence
            })
        
        return entities
    
    def batch_predict(self, texts: List[str]) -> List[Dict]:
        """批量预测"""
        results = []
        for text in texts:
            result = self.predict(text)
            results.append(result)
        return results
    
    def format_output(self, result: Dict, format_type: str = 'simple') -> str:
        """格式化输出结果"""
        if format_type == 'simple':
            output = f"文本: {result['text']}\n"
            output += "实体:\n"
            for entity in result['entities']:
                output += f"  {entity['text']} -> {entity['type']} (置信度: {entity['confidence']:.3f})\n"
            return output
        
        elif format_type == 'detailed':
            output = f"文本: {result['text']}\n"
            output += "字符级标注:\n"
            for char, label, conf in zip(result['chars'], result['labels'], result['confidence']):
                if label != 'O':
                    output += f"  {char}: {label} ({conf:.3f})\n"
            
            output += "\n实体:\n"
            for entity in result['entities']:
                output += f"  {entity['text']} -> {entity['type']} "
                output += f"[{entity['start']}:{entity['end']}] (置信度: {entity['confidence']:.3f})\n"
            return output
        
        elif format_type == 'json':
            return json.dumps(result, ensure_ascii=False, indent=2)
        
        else:
            return str(result)


def main():
    """命令行接口"""
    parser = argparse.ArgumentParser(description='NER模型推理')
    parser.add_argument('--model_path', type=str, default='./best_model', 
                       help='模型路径')
    parser.add_argument('--text', type=str, help='要预测的文本')
    parser.add_argument('--input_file', type=str, help='输入文件路径')
    parser.add_argument('--output_file', type=str, help='输出文件路径')
    parser.add_argument('--format', type=str, default='simple',
                       choices=['simple', 'detailed', 'json'],
                       help='输出格式')
    parser.add_argument('--interactive', action='store_true',
                       help='交互式模式')
    
    args = parser.parse_args()
    
    # 检查模型路径
    if not os.path.exists(args.model_path):
        print(f"模型路径不存在: {args.model_path}")
        return
    
    # 创建推理器
    inference = NERInference(args.model_path)
    
    if args.interactive:
        # 交互式模式
        print("NER模型推理 - 交互式模式")
        print("输入文本进行预测，输入 'quit' 退出")
        print("-" * 50)
        
        while True:
            text = input("\n请输入文本: ").strip()
            if text.lower() == 'quit':
                break
            
            if text:
                result = inference.predict(text)
                print(inference.format_output(result, args.format))
    
    elif args.text:
        # 单文本预测
        result = inference.predict(args.text)
        output = inference.format_output(result, args.format)
        
        if args.output_file:
            with open(args.output_file, 'w', encoding='utf-8') as f:
                f.write(output)
            print(f"结果已保存到: {args.output_file}")
        else:
            print(output)
    
    elif args.input_file:
        # 文件批量预测
        with open(args.input_file, 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        
        results = inference.batch_predict(texts)
        
        if args.output_file:
            with open(args.output_file, 'w', encoding='utf-8') as f:
                for result in results:
                    f.write(inference.format_output(result, args.format))
                    f.write("\n" + "-" * 50 + "\n")
            print(f"结果已保存到: {args.output_file}")
        else:
            for result in results:
                print(inference.format_output(result, args.format))
                print("-" * 50)
    
    else:
        # 演示模式
        demo_texts = [
            "张三在北京大学工作，月薪5000元",
            "李四于2024年1月在上海市参加春节联欢晚会",
            "阿里巴巴集团投资100万元开发新产品iPhone"
        ]
        
        print("NER模型推理演示")
        print("=" * 60)
        
        for text in demo_texts:
            result = inference.predict(text)
            print(inference.format_output(result, args.format))
            print("-" * 60)


if __name__ == "__main__":
    import os
    main()
