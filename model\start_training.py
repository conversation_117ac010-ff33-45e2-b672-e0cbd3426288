#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NER模型训练启动脚本
一键启动完整的训练流程
"""

import os
import sys
import logging
import argparse
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def check_dependencies():
    """检查依赖包"""
    logger.info("检查依赖包...")
    
    required_packages = [
        'torch', 'transformers', 'numpy', 'pandas', 
        'scikit-learn', 'seqeval', 'matplotlib', 'tqdm'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            logger.info(f"✓ {package}")
        except ImportError:
            missing_packages.append(package)
            logger.error(f"✗ {package}")
    
    if missing_packages:
        logger.error(f"缺少依赖包: {missing_packages}")
        logger.info("请运行: pip install -r requirements.txt")
        return False
    
    logger.info("所有依赖包检查通过")
    return True

def check_data():
    """检查数据文件"""
    logger.info("检查数据文件...")
    
    data_files = [
        "../data/ner_dataset_20250803_153737.json"
    ]
    
    for data_file in data_files:
        if os.path.exists(data_file):
            logger.info(f"✓ {data_file}")
        else:
            logger.error(f"✗ {data_file}")
            return False
    
    logger.info("数据文件检查通过")
    return True

def create_directories():
    """创建必要的目录"""
    logger.info("创建目录结构...")
    
    directories = [
        './models',
        './output',
        './logs',
        './cache',
        './best_model'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"✓ {directory}")

def run_data_preprocessing():
    """运行数据预处理"""
    logger.info("开始数据预处理...")
    
    try:
        from data_processor import NERDataProcessor
        
        processor = NERDataProcessor()
        
        # 加载数据
        data_path = "../data/ner_dataset_20250803_153737.json"
        data = processor.load_json_data(data_path)
        
        # 验证数据
        valid_data, errors = processor.validate_data(data)
        
        if errors:
            logger.warning(f"发现 {len(errors)} 个数据错误")
            for error in errors[:5]:
                logger.warning(f"  {error}")
        
        # 分析数据
        stats = processor.analyze_data(valid_data)
        logger.info(f"数据统计: {stats['total_samples']} 样本, {stats['total_entities']} 实体")
        
        # 分割数据
        train_data, val_data, test_data = processor.split_data(valid_data)
        
        # 保存分割后的数据
        processor.save_processed_data(train_data, "./output/train_data.json")
        processor.save_processed_data(val_data, "./output/val_data.json")
        processor.save_processed_data(test_data, "./output/test_data.json")
        
        logger.info("数据预处理完成")
        return True
        
    except Exception as e:
        logger.error(f"数据预处理失败: {e}")
        return False

def run_training():
    """运行模型训练"""
    logger.info("开始模型训练...")
    
    try:
        from train_ner_model import NERTrainer
        
        # 创建训练器
        trainer = NERTrainer()
        
        # 加载数据
        train_data, val_data, test_data = trainer.load_data("../data/ner_dataset_20250803_153737.json")
        
        # 训练模型
        trainer.train(train_data, val_data, epochs=5, batch_size=16)
        
        # 测试模型
        if test_data:
            test_loader = trainer.create_dataloader(test_data, batch_size=16, shuffle=False)
            test_f1 = trainer.evaluate(test_loader)
            logger.info(f"测试集F1分数: {test_f1:.4f}")
        
        # 保存最终模型
        trainer.save_model("chinese_ner_model")
        
        logger.info("模型训练完成")
        return True
        
    except Exception as e:
        logger.error(f"模型训练失败: {e}")
        return False

def run_evaluation():
    """运行模型评估"""
    logger.info("开始模型评估...")
    
    try:
        from evaluate_model import NERModelEvaluator
        import json
        
        # 检查模型和测试数据
        model_path = "./best_model"
        test_data_path = "./output/test_data.json"
        
        if not os.path.exists(model_path):
            logger.error(f"模型不存在: {model_path}")
            return False
        
        if not os.path.exists(test_data_path):
            logger.error(f"测试数据不存在: {test_data_path}")
            return False
        
        # 加载测试数据
        with open(test_data_path, 'r', encoding='utf-8') as f:
            test_data = json.load(f)
        
        # 创建评估器
        evaluator = NERModelEvaluator(model_path)
        
        # 评估模型
        results = evaluator.evaluate_dataset(test_data)
        
        # 打印结果
        evaluator.print_evaluation_results(results)
        
        # 保存报告
        evaluator.save_evaluation_report(results, "./output/evaluation_report.json")
        
        logger.info("模型评估完成")
        return True
        
    except Exception as e:
        logger.error(f"模型评估失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='NER模型训练启动脚本')
    parser.add_argument('--skip_deps', action='store_true', help='跳过依赖检查')
    parser.add_argument('--skip_data', action='store_true', help='跳过数据检查')
    parser.add_argument('--skip_preprocessing', action='store_true', help='跳过数据预处理')
    parser.add_argument('--skip_training', action='store_true', help='跳过模型训练')
    parser.add_argument('--skip_evaluation', action='store_true', help='跳过模型评估')
    
    args = parser.parse_args()
    
    logger.info("=" * 60)
    logger.info("NER模型训练启动")
    logger.info(f"开始时间: {datetime.now()}")
    logger.info("=" * 60)
    
    # 检查依赖
    if not args.skip_deps:
        if not check_dependencies():
            logger.error("依赖检查失败，退出")
            return
    
    # 检查数据
    if not args.skip_data:
        if not check_data():
            logger.error("数据检查失败，退出")
            return
    
    # 创建目录
    create_directories()
    
    # 数据预处理
    if not args.skip_preprocessing:
        if not run_data_preprocessing():
            logger.error("数据预处理失败，退出")
            return
    
    # 模型训练
    if not args.skip_training:
        if not run_training():
            logger.error("模型训练失败，退出")
            return
    
    # 模型评估
    if not args.skip_evaluation:
        if not run_evaluation():
            logger.error("模型评估失败")
            # 评估失败不退出，因为训练可能已经成功
    
    logger.info("=" * 60)
    logger.info("训练流程完成")
    logger.info(f"结束时间: {datetime.now()}")
    logger.info("=" * 60)
    
    # 显示结果文件
    logger.info("生成的文件:")
    result_files = [
        "./best_model",
        "./chinese_ner_model", 
        "./output/train_data.json",
        "./output/val_data.json",
        "./output/test_data.json",
        "./output/evaluation_report.json"
    ]
    
    for file_path in result_files:
        if os.path.exists(file_path):
            logger.info(f"  ✓ {file_path}")
        else:
            logger.info(f"  ✗ {file_path}")


if __name__ == "__main__":
    main()
