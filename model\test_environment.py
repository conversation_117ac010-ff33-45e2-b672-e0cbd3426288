#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模型训练环境
检查所有依赖和配置是否正确
"""

import sys
import os
import importlib

def test_python_version():
    """测试Python版本"""
    print("=== Python环境检查 ===")
    print(f"Python版本: {sys.version}")
    
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要3.7+")
        return False
    else:
        print("✅ Python版本符合要求")
        return True

def test_dependencies():
    """测试依赖包"""
    print("\n=== 依赖包检查 ===")
    
    dependencies = {
        'torch': 'PyTorch深度学习框架',
        'transformers': 'Hugging Face Transformers',
        'numpy': '数值计算库',
        'pandas': '数据处理库',
        'sklearn': 'scikit-learn机器学习库',
        'seqeval': 'NER评估库',
        'matplotlib': '绘图库',
        'tqdm': '进度条库'
    }
    
    success = True
    
    for package, description in dependencies.items():
        try:
            if package == 'sklearn':
                importlib.import_module('sklearn')
            else:
                importlib.import_module(package)
            print(f"✅ {package}: {description}")
        except ImportError:
            print(f"❌ {package}: {description} - 未安装")
            success = False
    
    return success

def test_torch_cuda():
    """测试PyTorch CUDA支持"""
    print("\n=== CUDA支持检查 ===")
    
    try:
        import torch
        print(f"PyTorch版本: {torch.__version__}")
        
        if torch.cuda.is_available():
            print(f"✅ CUDA可用")
            print(f"CUDA版本: {torch.version.cuda}")
            print(f"GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"  GPU {i}: {torch.cuda.get_device_name(i)}")
        else:
            print("⚠️ CUDA不可用，将使用CPU训练")
        
        return True
    except ImportError:
        print("❌ PyTorch未安装")
        return False

def test_transformers():
    """测试Transformers库"""
    print("\n=== Transformers库检查 ===")
    
    try:
        from transformers import BertTokenizer, BertForTokenClassification
        print(f"✅ Transformers库可用")
        
        # 测试加载预训练模型
        try:
            tokenizer = BertTokenizer.from_pretrained('bert-base-chinese')
            print("✅ 可以加载bert-base-chinese分词器")
        except Exception as e:
            print(f"⚠️ 无法加载bert-base-chinese分词器: {e}")
            print("   首次使用时会自动下载")
        
        return True
    except ImportError:
        print("❌ Transformers库未安装")
        return False

def test_data_files():
    """测试数据文件"""
    print("\n=== 数据文件检查 ===")
    
    data_files = [
        "../data/ner_dataset_20250803_153737.json",
        "../data/ner_dataset_20250803_153737.conll",
        "../data/ner_dataset_20250803_153737.bio"
    ]
    
    success = True
    
    for data_file in data_files:
        if os.path.exists(data_file):
            file_size = os.path.getsize(data_file) / (1024 * 1024)  # MB
            print(f"✅ {data_file} ({file_size:.2f} MB)")
        else:
            print(f"❌ {data_file} - 文件不存在")
            success = False
    
    return success

def test_directories():
    """测试目录结构"""
    print("\n=== 目录结构检查 ===")
    
    required_dirs = [
        './models',
        './output', 
        './logs',
        './cache'
    ]
    
    for directory in required_dirs:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
            print(f"✅ 创建目录: {directory}")
        else:
            print(f"✅ 目录存在: {directory}")
    
    return True

def test_memory():
    """测试内存"""
    print("\n=== 内存检查 ===")
    
    try:
        import psutil
        memory = psutil.virtual_memory()
        total_gb = memory.total / (1024**3)
        available_gb = memory.available / (1024**3)
        
        print(f"总内存: {total_gb:.2f} GB")
        print(f"可用内存: {available_gb:.2f} GB")
        
        if available_gb < 4:
            print("⚠️ 可用内存较少，建议减小batch_size")
        else:
            print("✅ 内存充足")
        
        return True
    except ImportError:
        print("⚠️ 无法检查内存（psutil未安装）")
        return True

def test_model_files():
    """测试模型相关文件"""
    print("\n=== 模型文件检查 ===")
    
    model_files = [
        './train_ner_model.py',
        './data_processor.py',
        './evaluate_model.py',
        './inference.py',
        './config.py'
    ]
    
    success = True
    
    for model_file in model_files:
        if os.path.exists(model_file):
            print(f"✅ {model_file}")
        else:
            print(f"❌ {model_file} - 文件不存在")
            success = False
    
    return success

def run_quick_test():
    """运行快速功能测试"""
    print("\n=== 快速功能测试 ===")
    
    try:
        # 测试数据处理
        from data_processor import NERDataProcessor
        processor = NERDataProcessor()
        print("✅ 数据处理器可用")
        
        # 测试配置
        from config import get_config
        config = get_config()
        print("✅ 配置文件可用")
        
        return True
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("NER模型训练环境测试")
    print("=" * 60)
    
    tests = [
        ("Python版本", test_python_version),
        ("依赖包", test_dependencies),
        ("CUDA支持", test_torch_cuda),
        ("Transformers", test_transformers),
        ("数据文件", test_data_files),
        ("目录结构", test_directories),
        ("内存", test_memory),
        ("模型文件", test_model_files),
        ("功能测试", run_quick_test)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试出错: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过，可以开始训练模型！")
        print("\n建议的下一步:")
        print("1. 运行: python start_training.py")
        print("2. 或者分步运行:")
        print("   - python data_processor.py")
        print("   - python train_ner_model.py")
        print("   - python evaluate_model.py")
    else:
        print("⚠️ 部分测试失败，请解决问题后再开始训练")
        print("\n解决建议:")
        print("1. 安装缺失的依赖: pip install -r requirements.txt")
        print("2. 检查数据文件是否存在")
        print("3. 确保有足够的内存和存储空间")

if __name__ == "__main__":
    main()
