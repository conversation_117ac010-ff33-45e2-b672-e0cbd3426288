#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文NER模型训练脚本
基于BERT进行字符级别的命名实体识别
"""

import os
import json
import torch
import numpy as np
from torch.utils.data import Dataset, DataLoader
from transformers import (
    BertTokenizerFast, BertForTokenClassification,
    get_linear_schedule_with_warmup
)
from torch.optim import AdamW
from sklearn.metrics import classification_report, f1_score
from tqdm import tqdm
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NERDataset(Dataset):
    """NER数据集类"""
    
    def __init__(self, data, tokenizer, label2id, max_length=128):
        self.data = data
        self.tokenizer = tokenizer
        self.label2id = label2id
        self.max_length = max_length
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        chars = item['chars']
        tags = item['tags']
        
        # 转换为BERT输入格式
        encoding = self.tokenizer(
            chars,
            is_split_into_words=True,
            padding='max_length',
            truncation=True,
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        # 处理标签
        labels = []
        word_ids = encoding.word_ids()
        previous_word_idx = None
        
        for word_idx in word_ids:
            if word_idx is None:
                labels.append(-100)  # 特殊token不参与损失计算
            elif word_idx != previous_word_idx:
                labels.append(self.label2id[tags[word_idx]])
            else:
                labels.append(-100)  # 子词不参与损失计算
            previous_word_idx = word_idx
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(labels, dtype=torch.long)
        }

class NERTrainer:
    """NER模型训练器"""
    
    def __init__(self, model_name='bert-base-chinese'):
        self.model_name = model_name
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"使用设备: {self.device}")
        
        # 标签映射
        self.labels = [
            'O', 'B-PER', 'I-PER', 'B-ORG', 'I-ORG', 'B-LOC', 'I-LOC',
            'B-TIME', 'I-TIME', 'B-MONEY', 'I-MONEY', 'B-PRODUCT', 'I-PRODUCT',
            'B-EVENT', 'I-EVENT'
        ]
        self.label2id = {label: i for i, label in enumerate(self.labels)}
        self.id2label = {i: label for i, label in enumerate(self.labels)}
        
        # 初始化模型和分词器
        self.tokenizer = BertTokenizerFast.from_pretrained(model_name)
        self.model = BertForTokenClassification.from_pretrained(
            model_name,
            num_labels=len(self.labels),
            id2label=self.id2label,
            label2id=self.label2id
        )
        self.model.to(self.device)
    
    def load_data(self, data_path):
        """加载训练数据"""
        logger.info(f"加载数据: {data_path}")
        with open(data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 数据分割
        train_size = int(0.8 * len(data))
        val_size = int(0.1 * len(data))
        
        train_data = data[:train_size]
        val_data = data[train_size:train_size + val_size]
        test_data = data[train_size + val_size:]
        
        logger.info(f"训练集: {len(train_data)} 条")
        logger.info(f"验证集: {len(val_data)} 条")
        logger.info(f"测试集: {len(test_data)} 条")
        
        return train_data, val_data, test_data
    
    def create_dataloader(self, data, batch_size=16, shuffle=True):
        """创建数据加载器"""
        dataset = NERDataset(data, self.tokenizer, self.label2id)
        return DataLoader(dataset, batch_size=batch_size, shuffle=shuffle)
    
    def train(self, train_data, val_data, epochs=3, batch_size=16, learning_rate=2e-5):
        """训练模型"""
        train_loader = self.create_dataloader(train_data, batch_size, shuffle=True)
        val_loader = self.create_dataloader(val_data, batch_size, shuffle=False)
        
        # 优化器和调度器
        optimizer = AdamW(self.model.parameters(), lr=learning_rate)
        total_steps = len(train_loader) * epochs
        scheduler = get_linear_schedule_with_warmup(
            optimizer,
            num_warmup_steps=0,
            num_training_steps=total_steps
        )
        
        best_f1 = 0
        
        for epoch in range(epochs):
            logger.info(f"Epoch {epoch + 1}/{epochs}")
            
            # 训练
            self.model.train()
            total_loss = 0
            
            for batch in tqdm(train_loader, desc="训练"):
                optimizer.zero_grad()
                
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                labels = batch['labels'].to(self.device)
                
                outputs = self.model(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    labels=labels
                )
                
                loss = outputs.loss
                loss.backward()
                optimizer.step()
                scheduler.step()
                
                total_loss += loss.item()
            
            avg_loss = total_loss / len(train_loader)
            logger.info(f"平均训练损失: {avg_loss:.4f}")
            
            # 验证
            val_f1 = self.evaluate(val_loader)
            logger.info(f"验证F1分数: {val_f1:.4f}")
            
            # 保存最佳模型
            if val_f1 > best_f1:
                best_f1 = val_f1
                self.save_model("best_model")
                logger.info(f"保存最佳模型，F1: {best_f1:.4f}")
    
    def evaluate(self, dataloader):
        """评估模型"""
        self.model.eval()
        predictions = []
        true_labels = []
        
        with torch.no_grad():
            for batch in dataloader:
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                labels = batch['labels'].to(self.device)
                
                outputs = self.model(
                    input_ids=input_ids,
                    attention_mask=attention_mask
                )
                
                logits = outputs.logits
                predictions.extend(torch.argmax(logits, dim=-1).cpu().numpy())
                true_labels.extend(labels.cpu().numpy())
        
        # 计算F1分数
        flat_predictions = []
        flat_true_labels = []
        
        for pred_seq, true_seq in zip(predictions, true_labels):
            for pred, true in zip(pred_seq, true_seq):
                if true != -100:  # 忽略特殊token
                    flat_predictions.append(pred)
                    flat_true_labels.append(true)
        
        f1 = f1_score(flat_true_labels, flat_predictions, average='weighted')
        return f1
    
    def save_model(self, save_path):
        """保存模型"""
        os.makedirs(save_path, exist_ok=True)
        self.model.save_pretrained(save_path)
        self.tokenizer.save_pretrained(save_path)
        
        # 保存标签映射
        with open(os.path.join(save_path, 'label_mapping.json'), 'w', encoding='utf-8') as f:
            json.dump({
                'label2id': self.label2id,
                'id2label': self.id2label,
                'labels': self.labels
            }, f, ensure_ascii=False, indent=2)
    
    def load_model(self, model_path):
        """加载模型"""
        self.model = BertForTokenClassification.from_pretrained(model_path)
        self.tokenizer = BertTokenizerFast.from_pretrained(model_path)
        self.model.to(self.device)
        
        # 加载标签映射
        with open(os.path.join(model_path, 'label_mapping.json'), 'r', encoding='utf-8') as f:
            mapping = json.load(f)
            self.label2id = mapping['label2id']
            self.id2label = {int(k): v for k, v in mapping['id2label'].items()}
            self.labels = mapping['labels']


def main():
    """主训练函数"""
    # 配置
    data_path = "../data/ner_dataset_20250803_153737.json"
    model_save_path = "chinese_ner_model"
    
    # 检查数据文件
    if not os.path.exists(data_path):
        logger.error(f"数据文件不存在: {data_path}")
        return
    
    # 创建训练器
    trainer = NERTrainer()
    
    # 加载数据
    train_data, val_data, test_data = trainer.load_data(data_path)
    
    # 训练模型
    logger.info("开始训练模型...")
    trainer.train(train_data, val_data, epochs=3, batch_size=16)
    
    # 测试模型
    if test_data:
        test_loader = trainer.create_dataloader(test_data, batch_size=16, shuffle=False)
        test_f1 = trainer.evaluate(test_loader)
        logger.info(f"测试集F1分数: {test_f1:.4f}")
    
    # 保存最终模型
    trainer.save_model(model_save_path)
    logger.info(f"模型已保存到: {model_save_path}")


if __name__ == "__main__":
    main()
