#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NER数据标注可视化界面
支持数据验证、可疑数据检查和手动标注修正
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import json
import re
from typing import List, Dict, Tuple
from collections import defaultdict, Counter
import threading

class NERAnnotationUI:
    def __init__(self, root):
        self.root = root
        self.root.title("NER数据标注工具")
        self.root.geometry("1400x900")
        
        # 数据存储
        self.dataset = []
        self.current_index = 0
        self.suspicious_cases = []
        self.current_suspicious_index = 0
        self.modified_samples = set()
        
        # 实体类型颜色映射
        self.entity_colors = {
            'PER': '#FFB6C1',      # 浅粉色
            'ORG': '#87CEEB',      # 天蓝色
            'LOC': '#98FB98',      # 浅绿色
            'TIME': '#DDA0DD',     # 梅花色
            'MONEY': '#F0E68C',    # 卡其色
            'PRODUCT': '#FFA07A',  # 浅鲑鱼色
            'EVENT': '#20B2AA'     # 浅海绿色
        }
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 顶部工具栏
        self.create_toolbar(main_frame)
        
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # 数据浏览标签页
        self.create_data_browser_tab()
        
        # 可疑数据检查标签页
        self.create_suspicious_data_tab()
        
        # 手动标注标签页
        self.create_manual_annotation_tab()
        
        # 统计信息标签页
        self.create_statistics_tab()
        
    def create_toolbar(self, parent):
        """创建工具栏"""
        toolbar = ttk.Frame(parent)
        toolbar.pack(fill=tk.X, pady=(0, 10))
        
        # 文件操作按钮
        ttk.Button(toolbar, text="加载数据集", command=self.load_dataset).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="保存修改", command=self.save_dataset).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="验证数据", command=self.validate_data).pack(side=tk.LEFT, padx=(0, 5))
        
        # 分隔符
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        # 状态标签
        self.status_label = ttk.Label(toolbar, text="请加载数据集")
        self.status_label.pack(side=tk.LEFT, padx=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(toolbar, variable=self.progress_var, length=200)
        self.progress_bar.pack(side=tk.RIGHT, padx=(10, 0))
        
    def create_data_browser_tab(self):
        """创建数据浏览标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="数据浏览")
        
        # 导航框架
        nav_frame = ttk.Frame(frame)
        nav_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(nav_frame, text="上一条", command=self.prev_sample).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(nav_frame, text="下一条", command=self.next_sample).pack(side=tk.LEFT, padx=(0, 5))
        
        # 跳转框架
        jump_frame = ttk.Frame(nav_frame)
        jump_frame.pack(side=tk.LEFT, padx=(20, 0))
        
        ttk.Label(jump_frame, text="跳转到:").pack(side=tk.LEFT)
        self.jump_entry = ttk.Entry(jump_frame, width=10)
        self.jump_entry.pack(side=tk.LEFT, padx=(5, 5))
        ttk.Button(jump_frame, text="跳转", command=self.jump_to_sample).pack(side=tk.LEFT)
        
        # 样本信息框架
        info_frame = ttk.LabelFrame(frame, text="样本信息")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.sample_info_text = tk.Text(info_frame, height=3, wrap=tk.WORD)
        self.sample_info_text.pack(fill=tk.X, padx=5, pady=5)
        
        # 句子显示框架
        sentence_frame = ttk.LabelFrame(frame, text="句子标注显示")
        sentence_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建带滚动条的文本框
        text_frame = ttk.Frame(sentence_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.sentence_text = tk.Text(text_frame, wrap=tk.WORD, font=("Arial", 14))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.sentence_text.yview)
        self.sentence_text.configure(yscrollcommand=scrollbar.set)
        
        self.sentence_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 配置标签颜色
        for entity_type, color in self.entity_colors.items():
            self.sentence_text.tag_configure(entity_type, background=color, foreground="black")
        
    def create_suspicious_data_tab(self):
        """创建可疑数据检查标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="可疑数据检查")
        
        # 控制框架
        control_frame = ttk.Frame(frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(control_frame, text="检测可疑数据", command=self.detect_suspicious_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="上一个可疑", command=self.prev_suspicious).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="下一个可疑", command=self.next_suspicious).pack(side=tk.LEFT, padx=(0, 5))
        
        # 可疑数据信息
        self.suspicious_info_label = ttk.Label(control_frame, text="未检测")
        self.suspicious_info_label.pack(side=tk.LEFT, padx=(20, 0))
        
        # 可疑数据显示
        suspicious_frame = ttk.LabelFrame(frame, text="可疑数据详情")
        suspicious_frame.pack(fill=tk.BOTH, expand=True)
        
        self.suspicious_text = scrolledtext.ScrolledText(suspicious_frame, wrap=tk.WORD, font=("Arial", 12))
        self.suspicious_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 操作按钮框架
        action_frame = ttk.Frame(suspicious_frame)
        action_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(action_frame, text="标记为正确", command=self.mark_as_correct).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(action_frame, text="需要修正", command=self.mark_for_correction).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(action_frame, text="跳转到标注页", command=self.goto_annotation).pack(side=tk.LEFT, padx=(0, 5))
        
    def create_manual_annotation_tab(self):
        """创建手动标注标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="手动标注")
        
        # 分割为左右两部分
        paned = ttk.PanedWindow(frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True)
        
        # 左侧：句子编辑
        left_frame = ttk.LabelFrame(paned, text="句子编辑")
        paned.add(left_frame, weight=2)
        
        # 句子输入
        ttk.Label(left_frame, text="句子:").pack(anchor=tk.W, padx=5, pady=(5, 0))
        self.edit_sentence_entry = tk.Text(left_frame, height=3, wrap=tk.WORD, font=("Arial", 12))
        self.edit_sentence_entry.pack(fill=tk.X, padx=5, pady=5)
        
        # 字符标签编辑
        ttk.Label(left_frame, text="字符标签 (每行一个字符和标签，用制表符分隔):").pack(anchor=tk.W, padx=5)
        self.edit_tags_text = scrolledtext.ScrolledText(left_frame, wrap=tk.NONE, font=("Courier", 10))
        self.edit_tags_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 编辑按钮
        edit_buttons = ttk.Frame(left_frame)
        edit_buttons.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(edit_buttons, text="加载当前样本", command=self.load_current_sample_for_edit).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(edit_buttons, text="应用修改", command=self.apply_edit).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(edit_buttons, text="重置", command=self.reset_edit).pack(side=tk.LEFT, padx=(0, 5))
        
        # 右侧：实体类型参考
        right_frame = ttk.LabelFrame(paned, text="标注参考")
        paned.add(right_frame, weight=1)
        
        # 实体类型说明
        entity_info = """实体类型说明:
        
PER - 人名
格式: B-PER, I-PER
示例: 张三 -> 张(B-PER) 三(I-PER)

ORG - 组织机构
格式: B-ORG, I-ORG
示例: 北京大学 -> 北(B-ORG) 京(I-ORG) 大(I-ORG) 学(I-ORG)

LOC - 地点
格式: B-LOC, I-LOC
示例: 北京市 -> 北(B-LOC) 京(I-LOC) 市(I-LOC)

TIME - 时间
格式: B-TIME, I-TIME
示例: 2024年 -> 2(B-TIME) 0(I-TIME) 2(I-TIME) 4(I-TIME) 年(I-TIME)

MONEY - 金额
格式: B-MONEY, I-MONEY
示例: 100万元 -> 1(B-MONEY) 0(I-MONEY) 0(I-MONEY) 万(I-MONEY) 元(I-MONEY)

PRODUCT - 产品
格式: B-PRODUCT, I-PRODUCT
示例: iPhone -> i(B-PRODUCT) P(I-PRODUCT) h(I-PRODUCT) o(I-PRODUCT) n(I-PRODUCT) e(I-PRODUCT)

EVENT - 事件
格式: B-EVENT, I-EVENT
示例: 世界杯 -> 世(B-EVENT) 界(I-EVENT) 杯(I-EVENT)

O - 非实体
格式: O
示例: 在、的、了等"""
        
        reference_text = tk.Text(right_frame, wrap=tk.WORD, font=("Arial", 10))
        reference_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        reference_text.insert(tk.END, entity_info)
        reference_text.config(state=tk.DISABLED)
        
    def create_statistics_tab(self):
        """创建统计信息标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="统计信息")
        
        self.stats_text = scrolledtext.ScrolledText(frame, wrap=tk.WORD, font=("Arial", 11))
        self.stats_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 刷新按钮
        ttk.Button(frame, text="刷新统计", command=self.update_statistics).pack(pady=5)

    def load_dataset(self):
        """加载数据集"""
        file_path = filedialog.askopenfilename(
            title="选择NER数据集文件",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.dataset = json.load(f)

                self.current_index = 0
                self.modified_samples.clear()
                self.suspicious_cases.clear()

                self.status_label.config(text=f"已加载 {len(self.dataset)} 条数据")
                self.display_current_sample()
                self.update_statistics()

                messagebox.showinfo("成功", f"成功加载 {len(self.dataset)} 条数据")

            except Exception as e:
                messagebox.showerror("错误", f"加载数据集失败: {str(e)}")

    def save_dataset(self):
        """保存数据集"""
        if not self.dataset:
            messagebox.showwarning("警告", "没有数据可保存")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存数据集",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.dataset, f, ensure_ascii=False, indent=2)

                self.modified_samples.clear()
                messagebox.showinfo("成功", f"数据集已保存到: {file_path}")

            except Exception as e:
                messagebox.showerror("错误", f"保存数据集失败: {str(e)}")

    def display_current_sample(self):
        """显示当前样本"""
        if not self.dataset or self.current_index >= len(self.dataset):
            # 如果没有数据，清空显示
            self.sample_info_text.delete(1.0, tk.END)
            self.sample_info_text.insert(tk.END, "没有数据")
            self.sentence_text.config(state=tk.NORMAL)
            self.sentence_text.delete(1.0, tk.END)
            self.sentence_text.config(state=tk.DISABLED)
            return

        sample = self.dataset[self.current_index]

        # 更新样本信息
        info_text = f"样本ID: {sample['id']}  |  索引: {self.current_index + 1}/{len(self.dataset)}  |  长度: {sample['length']} 字符"
        if sample['id'] in self.modified_samples:
            info_text += "  |  [已修改]"

        self.sample_info_text.delete(1.0, tk.END)
        self.sample_info_text.insert(tk.END, info_text)
        self.sample_info_text.insert(tk.END, f"\n句子: {sample['sentence']}")

        # 显示带标注的句子
        self.display_annotated_sentence(sample)

        # 更新跳转输入框
        self.jump_entry.delete(0, tk.END)
        self.jump_entry.insert(0, str(self.current_index + 1))

        # 强制刷新界面
        self.root.update_idletasks()

    def display_annotated_sentence(self, sample):
        """显示带标注的句子"""
        # 先启用编辑状态以便清空和插入内容
        self.sentence_text.config(state=tk.NORMAL)
        self.sentence_text.delete(1.0, tk.END)

        chars = sample['chars']
        tags = sample['tags']

        current_pos = 1.0
        i = 0

        while i < len(chars):
            char = chars[i]
            tag = tags[i]

            if tag.startswith('B-'):
                # 开始一个新实体
                entity_type = tag[2:]
                entity_text = char
                i += 1

                # 收集完整实体
                while i < len(chars) and tags[i] == f'I-{entity_type}':
                    entity_text += chars[i]
                    i += 1

                # 插入实体文本并应用标签
                self.sentence_text.insert(current_pos, entity_text)
                entity_end = f"{current_pos}+{len(entity_text)}c"
                self.sentence_text.tag_add(entity_type, current_pos, entity_end)

                current_pos = entity_end
            else:
                # 非实体字符
                self.sentence_text.insert(current_pos, char)
                current_pos = f"{current_pos}+1c"
                i += 1

        # 显示完成后设置为只读状态
        self.sentence_text.config(state=tk.DISABLED)

    def prev_sample(self):
        """上一个样本"""
        if self.current_index > 0:
            self.current_index -= 1
            self.display_current_sample()

    def next_sample(self):
        """下一个样本"""
        if self.current_index < len(self.dataset) - 1:
            self.current_index += 1
            self.display_current_sample()

    def jump_to_sample(self):
        """跳转到指定样本"""
        try:
            index = int(self.jump_entry.get()) - 1
            if 0 <= index < len(self.dataset):
                self.current_index = index
                self.display_current_sample()
            else:
                messagebox.showwarning("警告", f"索引超出范围 (1-{len(self.dataset)})")
        except ValueError:
            messagebox.showwarning("警告", "请输入有效的数字")

    def validate_data(self):
        """验证数据"""
        if not self.dataset:
            messagebox.showwarning("警告", "请先加载数据集")
            return

        # 在新线程中运行验证，避免界面冻结
        def run_validation():
            self.progress_var.set(0)
            self.status_label.config(text="正在验证数据...")

            try:
                from comprehensive_validator import ComprehensiveValidator
                validator = ComprehensiveValidator()

                # 简化验证，只检查关键问题
                errors = []
                warnings = []

                for i, sample in enumerate(self.dataset):
                    if i % 100 == 0:
                        progress = (i / len(self.dataset)) * 100
                        self.progress_var.set(progress)
                        self.root.update_idletasks()

                    # 检查BIO格式
                    chars = sample['chars']
                    tags = sample['tags']

                    if len(chars) != len(tags):
                        errors.append(f"样本{sample['id']}: 字符数与标签数不匹配")
                        continue

                    # 检查I标签的前置条件
                    for j, tag in enumerate(tags):
                        if tag.startswith('I-'):
                            if j == 0:
                                errors.append(f"样本{sample['id']}: I标签不能出现在句子开头")
                            else:
                                prev_tag = tags[j-1]
                                entity_type = tag[2:]
                                if prev_tag not in [f'B-{entity_type}', f'I-{entity_type}']:
                                    errors.append(f"样本{sample['id']}: I标签前置条件错误")

                self.progress_var.set(100)

                if errors:
                    result = f"发现 {len(errors)} 个错误:\n\n"
                    result += "\n".join(errors[:20])
                    if len(errors) > 20:
                        result += f"\n... 还有 {len(errors) - 20} 个错误"

                    messagebox.showerror("验证结果", result)
                else:
                    messagebox.showinfo("验证结果", "数据验证通过！")

                self.status_label.config(text=f"验证完成 - {len(errors)} 个错误")

            except Exception as e:
                messagebox.showerror("错误", f"验证过程出错: {str(e)}")
            finally:
                self.progress_var.set(0)

        threading.Thread(target=run_validation, daemon=True).start()

    def detect_suspicious_data(self):
        """检测可疑数据"""
        if not self.dataset:
            messagebox.showwarning("警告", "请先加载数据集")
            return

        def run_detection():
            self.progress_var.set(0)
            self.status_label.config(text="正在检测可疑数据...")
            self.suspicious_cases.clear()

            try:
                # 检测可疑模式
                for i, sample in enumerate(self.dataset):
                    if i % 100 == 0:
                        progress = (i / len(self.dataset)) * 100
                        self.progress_var.set(progress)
                        self.root.update_idletasks()

                    # 检测单字符实体
                    entities = self.extract_entities(sample['chars'], sample['tags'])
                    for entity_text, entity_type, _, _ in entities:
                        if len(entity_text) == 1 and entity_type in ['PER', 'ORG', 'LOC']:
                            self.suspicious_cases.append({
                                'sample_id': sample['id'],
                                'index': i,
                                'type': '单字符实体',
                                'description': f"{entity_type}实体 '{entity_text}' 只有一个字符",
                                'sentence': sample['sentence'],
                                'entity': entity_text,
                                'entity_type': entity_type
                            })

                        # 检测可疑人名
                        if entity_type == 'PER':
                            if any(stop in entity_text for stop in ['于', '在', '和', '与']):
                                self.suspicious_cases.append({
                                    'sample_id': sample['id'],
                                    'index': i,
                                    'type': '可疑人名',
                                    'description': f"人名 '{entity_text}' 包含停用词",
                                    'sentence': sample['sentence'],
                                    'entity': entity_text,
                                    'entity_type': entity_type
                                })

                self.progress_var.set(100)
                self.current_suspicious_index = 0

                if self.suspicious_cases:
                    self.suspicious_info_label.config(text=f"发现 {len(self.suspicious_cases)} 个可疑情况")
                    self.display_current_suspicious()
                    messagebox.showinfo("检测完成", f"发现 {len(self.suspicious_cases)} 个可疑情况")
                else:
                    self.suspicious_info_label.config(text="未发现可疑情况")
                    self.suspicious_text.delete(1.0, tk.END)
                    self.suspicious_text.insert(tk.END, "未发现可疑情况")
                    messagebox.showinfo("检测完成", "未发现可疑情况")

                self.status_label.config(text=f"检测完成 - {len(self.suspicious_cases)} 个可疑情况")

            except Exception as e:
                messagebox.showerror("错误", f"检测过程出错: {str(e)}")
            finally:
                self.progress_var.set(0)

        threading.Thread(target=run_detection, daemon=True).start()

    def extract_entities(self, chars, tags):
        """提取实体"""
        entities = []
        current_entity = ""
        current_type = ""
        start_pos = -1

        for i, (char, tag) in enumerate(zip(chars, tags)):
            if tag.startswith('B-'):
                if current_entity:
                    entities.append((current_entity, current_type, start_pos, i-1))
                current_entity = char
                current_type = tag[2:]
                start_pos = i
            elif tag.startswith('I-') and current_entity:
                current_entity += char
            else:
                if current_entity:
                    entities.append((current_entity, current_type, start_pos, i-1))
                    current_entity = ""
                    current_type = ""
                    start_pos = -1

        if current_entity:
            entities.append((current_entity, current_type, start_pos, len(chars)-1))

        return entities

    def display_current_suspicious(self):
        """显示当前可疑情况"""
        if not self.suspicious_cases or self.current_suspicious_index >= len(self.suspicious_cases):
            return

        case = self.suspicious_cases[self.current_suspicious_index]

        self.suspicious_info_label.config(
            text=f"可疑情况 {self.current_suspicious_index + 1}/{len(self.suspicious_cases)}"
        )

        display_text = f"样本ID: {case['sample_id']}\n"
        display_text += f"类型: {case['type']}\n"
        display_text += f"描述: {case['description']}\n"
        display_text += f"句子: {case['sentence']}\n\n"

        # 显示实体上下文
        if 'entity' in case:
            sentence = case['sentence']
            entity = case['entity']
            pos = sentence.find(entity)
            if pos != -1:
                before = sentence[max(0, pos-10):pos]
                after = sentence[pos+len(entity):pos+len(entity)+10]
                display_text += f"上下文: ...{before}[{entity}]{after}...\n"

        self.suspicious_text.delete(1.0, tk.END)
        self.suspicious_text.insert(tk.END, display_text)

    def prev_suspicious(self):
        """上一个可疑情况"""
        if self.current_suspicious_index > 0:
            self.current_suspicious_index -= 1
            self.display_current_suspicious()

    def next_suspicious(self):
        """下一个可疑情况"""
        if self.current_suspicious_index < len(self.suspicious_cases) - 1:
            self.current_suspicious_index += 1
            self.display_current_suspicious()

    def mark_as_correct(self):
        """标记为正确"""
        if self.suspicious_cases and self.current_suspicious_index < len(self.suspicious_cases):
            case = self.suspicious_cases[self.current_suspicious_index]
            messagebox.showinfo("标记", f"样本 {case['sample_id']} 已标记为正确")
            # 可以在这里添加标记逻辑

    def mark_for_correction(self):
        """标记需要修正"""
        if self.suspicious_cases and self.current_suspicious_index < len(self.suspicious_cases):
            case = self.suspicious_cases[self.current_suspicious_index]
            messagebox.showinfo("标记", f"样本 {case['sample_id']} 已标记为需要修正")
            # 可以在这里添加标记逻辑

    def goto_annotation(self):
        """跳转到标注页面"""
        if self.suspicious_cases and self.current_suspicious_index < len(self.suspicious_cases):
            case = self.suspicious_cases[self.current_suspicious_index]
            self.current_index = case['index']
            self.notebook.select(2)  # 切换到手动标注标签页
            self.load_current_sample_for_edit()

    def load_current_sample_for_edit(self):
        """加载当前样本到编辑器"""
        if not self.dataset or self.current_index >= len(self.dataset):
            return

        sample = self.dataset[self.current_index]

        # 加载句子
        self.edit_sentence_entry.delete(1.0, tk.END)
        self.edit_sentence_entry.insert(tk.END, sample['sentence'])

        # 加载字符标签对
        self.edit_tags_text.delete(1.0, tk.END)
        for char, tag in zip(sample['chars'], sample['tags']):
            self.edit_tags_text.insert(tk.END, f"{char}\t{tag}\n")

    def apply_edit(self):
        """应用编辑修改"""
        if not self.dataset or self.current_index >= len(self.dataset):
            return

        try:
            # 获取编辑的句子
            new_sentence = self.edit_sentence_entry.get(1.0, tk.END).strip()

            # 解析字符标签对
            lines = self.edit_tags_text.get(1.0, tk.END).strip().split('\n')
            new_chars = []
            new_tags = []

            for line in lines:
                if '\t' in line:
                    char, tag = line.split('\t', 1)
                    new_chars.append(char)
                    new_tags.append(tag.strip())

            # 验证数据
            if ''.join(new_chars) != new_sentence:
                messagebox.showerror("错误", "字符序列与句子不匹配")
                return

            # 验证BIO格式
            for i, tag in enumerate(new_tags):
                if tag.startswith('I-'):
                    if i == 0:
                        messagebox.showerror("错误", f"I标签不能出现在句子开头 (位置{i+1})")
                        return
                    else:
                        prev_tag = new_tags[i-1]
                        entity_type = tag[2:]
                        if prev_tag not in [f'B-{entity_type}', f'I-{entity_type}']:
                            messagebox.showerror("错误", f"I标签前置条件错误 (位置{i+1})")
                            return

            # 应用修改
            sample = self.dataset[self.current_index]
            sample['sentence'] = new_sentence
            sample['chars'] = new_chars
            sample['tags'] = new_tags
            sample['length'] = len(new_sentence)

            # 标记为已修改
            self.modified_samples.add(sample['id'])

            # 刷新显示
            self.display_current_sample()

            messagebox.showinfo("成功", "修改已应用")

        except Exception as e:
            messagebox.showerror("错误", f"应用修改失败: {str(e)}")

    def reset_edit(self):
        """重置编辑"""
        self.load_current_sample_for_edit()

    def update_statistics(self):
        """更新统计信息"""
        if not self.dataset:
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(tk.END, "请先加载数据集")
            return

        # 计算统计信息
        total_samples = len(self.dataset)
        total_chars = sum(sample['length'] for sample in self.dataset)
        avg_length = total_chars / total_samples if total_samples > 0 else 0

        # 实体统计
        entity_counts = Counter()
        total_entities = 0

        for sample in self.dataset:
            entities = self.extract_entities(sample['chars'], sample['tags'])
            for _, entity_type, _, _ in entities:
                entity_counts[entity_type] += 1
                total_entities += 1

        # 复杂度统计
        complexity_counts = Counter()
        for sample in self.dataset:
            complexity = sample.get('complexity', 'unknown')
            complexity_counts[complexity] += 1

        # 生成统计报告
        stats_text = f"数据集统计信息\n"
        stats_text += "=" * 50 + "\n\n"
        stats_text += f"总样本数: {total_samples:,}\n"
        stats_text += f"总字符数: {total_chars:,}\n"
        stats_text += f"平均句子长度: {avg_length:.2f} 字符\n"
        stats_text += f"已修改样本数: {len(self.modified_samples)}\n\n"

        stats_text += "实体类型分布:\n"
        stats_text += "-" * 30 + "\n"
        for entity_type, count in entity_counts.most_common():
            percentage = count / total_entities * 100 if total_entities > 0 else 0
            stats_text += f"{entity_type:8}: {count:6,} 个 ({percentage:5.1f}%)\n"

        if complexity_counts:
            stats_text += "\n句子复杂度分布:\n"
            stats_text += "-" * 30 + "\n"
            for complexity, count in complexity_counts.items():
                percentage = count / total_samples * 100
                stats_text += f"{complexity:8}: {count:6,} 条 ({percentage:5.1f}%)\n"

        # 长度分布
        lengths = [sample['length'] for sample in self.dataset]
        stats_text += f"\n句子长度统计:\n"
        stats_text += "-" * 30 + "\n"
        stats_text += f"最短句子: {min(lengths)} 字符\n"
        stats_text += f"最长句子: {max(lengths)} 字符\n"
        stats_text += f"中位数长度: {sorted(lengths)[len(lengths)//2]} 字符\n"

        # 可疑数据统计
        if hasattr(self, 'suspicious_cases') and self.suspicious_cases:
            stats_text += f"\n可疑数据统计:\n"
            stats_text += "-" * 30 + "\n"
            suspicious_types = Counter(case['type'] for case in self.suspicious_cases)
            for sus_type, count in suspicious_types.items():
                stats_text += f"{sus_type}: {count} 个\n"

        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(tk.END, stats_text)


def main():
    """主函数"""
    root = tk.Tk()
    app = NERAnnotationUI(root)

    # 设置窗口图标和其他属性
    root.minsize(1200, 800)

    # 绑定键盘快捷键
    root.bind('<Control-o>', lambda e: app.load_dataset())
    root.bind('<Control-s>', lambda e: app.save_dataset())
    root.bind('<Left>', lambda e: app.prev_sample())
    root.bind('<Right>', lambda e: app.next_sample())

    root.mainloop()


if __name__ == "__main__":
    main()
