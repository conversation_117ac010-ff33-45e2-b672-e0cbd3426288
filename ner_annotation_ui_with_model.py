#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NER数据标注可视化界面 - 集成智能模型版
支持数据验证、可疑数据检查、手动标注修正和智能预标注
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import json
import re
import os
import sys
from typing import List, Dict, Tuple
from collections import defaultdict, Counter
import threading

# 添加模型路径
model_path = os.path.join(os.path.dirname(__file__), 'model')
if model_path not in sys.path:
    sys.path.append(model_path)

# 导入模型推理模块
try:
    from model.inference import NERInference
    # 尝试导入改进的推理模块
    try:
        from improve_annotation_quality import ImprovedNERInference
        IMPROVED_MODEL_AVAILABLE = True
    except ImportError:
        IMPROVED_MODEL_AVAILABLE = False
    MODEL_AVAILABLE = True
except ImportError:
    MODEL_AVAILABLE = False
    IMPROVED_MODEL_AVAILABLE = False
    print("警告: 无法导入模型推理模块，智能预标注功能将不可用")

class EnhancedNERAnnotationUI:
    def __init__(self, root):
        self.root = root
        self.root.title("NER数据标注工具 - 智能增强版")
        self.root.geometry("1500x950")
        
        # 数据存储
        self.dataset = []
        self.current_index = 0
        self.suspicious_cases = []
        self.current_suspicious_index = 0
        self.modified_samples = set()
        
        # 模型相关
        self.model_inference = None
        self.improved_inference = None
        self.model_loaded = False
        self.use_improved_model = True  # 默认使用改进模型
        self.init_model()
        
        # 批量标注控制
        self.batch_annotation_running = False
        self.batch_annotation_stopped = False
        self.batch_stats = {
            'total': 0,
            'processed': 0,
            'success': 0,
            'failed': 0,
            'entities_found': 0
        }
        
        # 调试模式
        self.debug_mode = False
        
        # 实体类型颜色映射
        self.entity_colors = {
            'PER': '#FFB6C1',      # 浅粉色
            'ORG': '#87CEEB',      # 天蓝色
            'LOC': '#98FB98',      # 浅绿色
            'TIME': '#DDA0DD',     # 梅花色
            'MONEY': '#F0E68C',    # 卡其色
            'PRODUCT': '#FFA07A',  # 浅鲑鱼色
            'EVENT': '#20B2AA'     # 浅海绿色
        }
        
        self.setup_ui()

    def init_model(self):
        """初始化NER模型"""
        if not MODEL_AVAILABLE:
            return
            
        try:
            # 检查模型文件
            best_model_path = os.path.join(os.path.dirname(__file__), 'model', 'best_model')
            chinese_model_path = os.path.join(os.path.dirname(__file__), 'model', 'chinese_ner_model')
            
            model_path = None
            if os.path.exists(best_model_path):
                model_path = best_model_path
            elif os.path.exists(chinese_model_path):
                model_path = chinese_model_path
            
            if model_path:
                # 加载基础模型
                self.model_inference = NERInference(model_path)
                
                # 尝试加载改进模型
                if IMPROVED_MODEL_AVAILABLE:
                    try:
                        self.improved_inference = ImprovedNERInference(model_path)
                        print(f"✅ 改进模型加载成功: {model_path}")
                    except Exception as e:
                        print(f"⚠️ 改进模型加载失败，使用基础模型: {e}")
                        self.use_improved_model = False
                else:
                    print("⚠️ 改进模型不可用，使用基础模型")
                    self.use_improved_model = False
                
                self.model_loaded = True
                print(f"✅ 模型加载成功: {model_path}")
            else:
                print("⚠️  未找到训练好的模型文件")
                
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            self.model_loaded = False

    def predict_entities(self, text: str):
        """使用模型预测实体"""
        if not self.model_loaded:
            return None
            
        try:
            # 优先使用改进模型
            if self.use_improved_model and self.improved_inference:
                result = self.improved_inference.predict_with_improvements(text)
            else:
                result = self.model_inference.predict(text)
            return result
            
        except Exception as e:
            print(f"预测失败: {e}")
            # 如果改进模型失败，尝试基础模型
            if self.use_improved_model and self.model_inference:
                try:
                    print("尝试使用基础模型...")
                    result = self.model_inference.predict(text)
                    return result
                except Exception as e2:
                    print(f"基础模型也失败: {e2}")
            return None

    def auto_annotate_current_sample(self):
        """对当前样本进行智能预标注"""
        if not self.model_loaded:
            messagebox.showwarning("模型未加载", "智能预标注功能不可用，模型未正确加载")
            return
            
        if not self.dataset:
            messagebox.showwarning("无数据", "请先加载数据集")
            return
            
        try:
            # 获取当前样本的文本
            current_sample = self.dataset[self.current_index]
            text = current_sample.get('text', '')
            
            if not text:
                messagebox.showwarning("无文本", "当前样本没有文本内容")
                return
                
            # 显示预测进度
            progress_window = tk.Toplevel(self.root)
            progress_window.title("智能预标注中...")
            progress_window.geometry("300x100")
            progress_window.transient(self.root)
            progress_window.grab_set()
            
            ttk.Label(progress_window, text="正在使用AI模型进行预标注...").pack(pady=20)
            progress = ttk.Progressbar(progress_window, mode='indeterminate')
            progress.pack(pady=10)
            progress.start()
            
            # 在后台线程中进行预测
            def predict_in_background():
                try:
                    prediction_result = self.predict_entities(text)
                    
                    # 在主线程中更新UI
                    self.root.after(0, lambda: self.apply_predictions(prediction_result, progress_window))
                    
                except Exception as e:
                    self.root.after(0, lambda: self.handle_prediction_error(e, progress_window))
                    
            thread = threading.Thread(target=predict_in_background)
            thread.daemon = True
            thread.start()
            
        except Exception as e:
            messagebox.showerror("预标注失败", f"智能预标注过程中发生错误:\n{e}")

    def apply_predictions(self, prediction_result, progress_window):
        """应用预测结果到当前样本"""
        try:
            progress_window.destroy()
            
            if not prediction_result or not prediction_result.get('labels'):
                messagebox.showinfo("预标注完成", "模型没有预测到任何实体")
                return
                
            # 更新当前样本的标签
            current_sample = self.dataset[self.current_index]
            
            # 从预测结果中获取数据
            chars = prediction_result.get('chars', [])
            labels = prediction_result.get('labels', [])
            entities = prediction_result.get('entities', [])
            
            # 更新数据
            current_sample['text'] = ''.join(chars)
            current_sample['labels'] = labels
            
            # 标记为已修改
            self.modified_samples.add(self.current_index)
            
            # 刷新显示
            self.display_current_sample()
            
            # 统计预测结果
            entity_count = len(entities)
            entity_types = {}
            for entity in entities:
                entity_type = entity.get('type', 'UNKNOWN')
                entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
            
            # 构建实体统计信息
            entity_info = []
            for entity_type, count in entity_types.items():
                entity_info.append(f"{entity_type}: {count}个")
            
            # 检查是否使用了改进模型
            model_type = "改进模型" if (self.use_improved_model and self.improved_inference) else "基础模型"
            improvements_applied = prediction_result.get('improvements_applied', False)
            
            message = f"✅ 预标注完成！\n"
            message += f"📝 预测到 {entity_count} 个实体\n"
            message += f"🏷️ 实体分布: {', '.join(entity_info) if entity_info else '无'}\n"
            message += f"🎯 使用模型: {model_type}\n"
            
            if improvements_applied:
                message += f"⚡ 已应用质量改进规则\n"
            
            message += f"\n💡 提示: 请检查和调整预标注结果"
            
            messagebox.showinfo("智能预标注完成", message)
            
        except Exception as e:
            messagebox.showerror("应用预测失败", f"应用预测结果时发生错误:\n{e}")

    def handle_prediction_error(self, error, progress_window):
        """处理预测错误"""
        progress_window.destroy()
        messagebox.showerror("预测失败", f"智能预标注失败:\n{error}")

    def batch_auto_annotate(self):
        """批量智能预标注"""
        if not self.model_loaded:
            messagebox.showwarning("模型未加载", "智能预标注功能不可用，模型未正确加载")
            return
            
        if not self.dataset:
            messagebox.showwarning("无数据", "请先加载数据集")
            return
            
        if self.batch_annotation_running:
            messagebox.showwarning("正在运行", "批量标注已在进行中")
            return
        
        # 确定标注范围
        try:
            target_indices = self.get_batch_target_indices()
            if not target_indices:
                messagebox.showwarning("无目标", "没有找到符合条件的样本")
                return
                
        except Exception as e:
            messagebox.showerror("参数错误", f"批量标注参数设置错误:\n{e}")
            return
        
        # 确认开始批量标注
        result = messagebox.askyesno(
            "确认批量标注",
            f"将对 {len(target_indices)} 个样本进行智能预标注\n"
            f"这可能需要较长时间，是否继续？\n\n"
            f"💡 提示: 可以随时点击'停止'按钮中断处理"
        )
        
        if not result:
            return
        
        # 初始化批量标注状态
        self.batch_annotation_running = True
        self.batch_annotation_stopped = False
        self.batch_stats = {
            'total': len(target_indices),
            'processed': 0,
            'success': 0,
            'failed': 0,
            'entities_found': 0
        }
        
        # 更新UI状态
        self.batch_stop_btn.config(state=tk.NORMAL)
        self.batch_progress_bar.config(maximum=len(target_indices), value=0)
        self.update_batch_progress()
        
        # 在后台线程中进行批量处理
        thread = threading.Thread(target=self.batch_annotation_worker, args=(target_indices,))
        thread.daemon = True
        thread.start()

    def get_batch_target_indices(self):
        """获取批量标注的目标索引列表"""
        mode = self.batch_mode.get()
        
        if mode == "all":
            return list(range(len(self.dataset)))
            
        elif mode == "unlabeled":
            # 寻找未标注的样本（没有labels字段或labels为空）
            target_indices = []
            for i, sample in enumerate(self.dataset):
                labels = sample.get('labels', [])
                if not labels or all(label == 'O' for label in labels):
                    target_indices.append(i)
            return target_indices
            
        elif mode == "custom":
            start_str = self.start_index_entry.get().strip()
            end_str = self.end_index_entry.get().strip()
            
            if not start_str or not end_str:
                raise ValueError("请输入自定义范围的起始和结束位置")
                
            start_index = int(start_str) - 1  # 转换为0索引
            end_index = int(end_str) - 1
            
            if start_index < 0 or end_index >= len(self.dataset) or start_index > end_index:
                raise ValueError(f"自定义范围无效，应在1到{len(self.dataset)}之间")
                
            return list(range(start_index, end_index + 1))
        
        return []

    def batch_annotation_worker(self, target_indices):
        """批量标注工作线程"""
        try:
            for i, sample_index in enumerate(target_indices):
                if self.batch_annotation_stopped:
                    break
                    
                sample = self.dataset[sample_index]
                text = sample.get('text', '')
                
                if not text:
                    self.batch_stats['failed'] += 1
                    self.batch_stats['processed'] += 1
                    self.root.after(0, self.update_batch_progress)
                    continue
                
                try:
                    # 进行预测
                    prediction_result = self.predict_entities(text)
                    
                    if prediction_result and prediction_result.get('labels'):
                        # 应用预测结果
                        chars = prediction_result.get('chars', [])
                        labels = prediction_result.get('labels', [])
                        entities = prediction_result.get('entities', [])
                        
                        sample['text'] = ''.join(chars)
                        sample['labels'] = labels
                        
                        # 标记为已修改
                        self.modified_samples.add(sample_index)
                        
                        # 统计结果
                        self.batch_stats['success'] += 1
                        self.batch_stats['entities_found'] += len(entities)
                    else:
                        self.batch_stats['failed'] += 1
                        
                except Exception as e:
                    print(f"批量标注第{sample_index+1}个样本失败: {e}")
                    self.batch_stats['failed'] += 1
                
                self.batch_stats['processed'] += 1
                
                # 更新进度（在主线程中）
                self.root.after(0, self.update_batch_progress)
                
        except Exception as e:
            print(f"批量标注线程错误: {e}")
        
        finally:
            # 完成批量标注
            self.root.after(0, self.finish_batch_annotation)

    def update_batch_progress(self):
        """更新批量标注进度显示"""
        stats = self.batch_stats
        progress_text = f"进度: {stats['processed']}/{stats['total']} "
        
        if stats['processed'] > 0:
            percentage = (stats['processed'] / stats['total']) * 100
            progress_text += f"({percentage:.1f}%)"
        
        self.batch_progress_label.config(text=progress_text)
        self.batch_progress_bar.config(value=stats['processed'])
        
        # 更新统计信息
        stats_text = f"成功: {stats['success']} | 失败: {stats['failed']} | 发现实体: {stats['entities_found']}"
        self.batch_stats_label.config(text=stats_text)
        
        # 刷新当前显示（如果当前样本被修改了）
        if self.current_index in self.modified_samples:
            self.display_current_sample()
            
        # 更新总体统计
        self.update_stats()

    def finish_batch_annotation(self):
        """完成批量标注"""
        self.batch_annotation_running = False
        self.batch_stop_btn.config(state=tk.DISABLED)
        
        stats = self.batch_stats
        
        # 显示完成信息
        if self.batch_annotation_stopped:
            status = "批量标注已停止"
        else:
            status = "批量标注完成"
            
        messagebox.showinfo(
            status,
            f"📊 批量标注结果:\n"
            f"📋 处理样本: {stats['processed']}/{stats['total']}\n"
            f"✅ 成功标注: {stats['success']}\n"
            f"❌ 失败样本: {stats['failed']}\n"
            f"🏷️ 发现实体: {stats['entities_found']}\n\n"
            f"💾 请记得保存数据！"
        )

    def stop_batch_annotation(self):
        """停止批量标注"""
        if self.batch_annotation_running:
            result = messagebox.askyesno("确认停止", "确定要停止批量标注吗？\n已处理的数据将被保留。")
            if result:
                self.batch_annotation_stopped = True
                self.batch_progress_label.config(text="正在停止批量标注...")

    def show_batch_annotation_tab(self):
        """切换到智能标注页面"""
        self.notebook.select(3)  # 智能标注是第4个标签页（索引3）

    def toggle_debug_mode(self):
        """切换调试模式"""
        self.debug_mode = self.debug_var.get()
        
        if self.debug_mode:
            print("🔧 调试模式已启用")
        else:
            print("🔧 调试模式已关闭")
        
        # 重新显示当前样本以应用调试信息
        if self.dataset and 0 <= self.current_index < len(self.dataset):
            self.display_current_sample()

    def toggle_model_mode(self):
        """切换模型模式"""
        self.use_improved_model = self.model_mode_var.get()
        
        if self.use_improved_model:
            print("🚀 切换到改进模型")
            self.model_status_label.config(text="🚀 改进模型已加载", fg="blue")
        else:
            print("🤖 切换到基础模型")
            self.model_status_label.config(text="🤖 基础模型已加载", fg="green")

    def create_legend(self, parent):
        """创建实体类型图例"""
        # 图例容器
        legend_container = tk.Frame(parent, relief=tk.GROOVE, borderwidth=1, bg="#f0f0f0")
        legend_container.pack(fill=tk.X, pady=2)

        # 图例标题
        title_label = tk.Label(
            legend_container,
            text="🎨 实体类型颜色图例:",
            font=("Arial", 10, "bold"),
            bg="#f0f0f0"
        )
        title_label.pack(side=tk.LEFT, padx=(10, 15), pady=5)

        # 实体类型说明
        entity_descriptions = {
            'PER': '人名', 'ORG': '组织机构', 'LOC': '地点位置', 'TIME': '时间表达',
            'MONEY': '金钱数额', 'PRODUCT': '产品名称', 'EVENT': '事件活动'
        }

        # 创建图例项
        for entity_type, description in entity_descriptions.items():
            legend_item = tk.Label(
                legend_container,
                text=f" {entity_type} ",
                bg=self.entity_colors[entity_type],
                fg="black",
                font=("Arial", 9, "bold"),
                relief=tk.RAISED,
                borderwidth=1
            )
            legend_item.pack(side=tk.LEFT, padx=2, pady=2)

            # 添加描述
            desc_label = tk.Label(
                legend_container,
                text=description,
                font=("Arial", 8),
                bg="#f0f0f0"
            )
            desc_label.pack(side=tk.LEFT, padx=(0, 10), pady=2)

    def create_compact_legend(self, parent):
        """创建紧凑版图例"""
        # 图例容器
        legend_container = tk.Frame(parent, relief=tk.GROOVE, borderwidth=1, bg="#f8f8f8")
        legend_container.pack(fill=tk.X, pady=2)

        # 图例标题
        title_label = tk.Label(
            legend_container,
            text="🎨 颜色图例:",
            font=("Arial", 9, "bold"),
            bg="#f8f8f8"
        )
        title_label.pack(side=tk.LEFT, padx=(5, 8), pady=1)

        # 实体类型说明（紧凑版）
        entity_descriptions = {
            'PER': '人名', 'ORG': '组织', 'LOC': '地点', 'TIME': '时间',
            'MONEY': '金额', 'PRODUCT': '产品', 'EVENT': '事件'
        }

        # 创建紧凑图例项
        for entity_type, description in entity_descriptions.items():
            legend_item = tk.Label(
                legend_container,
                text=f" {entity_type}:{description} ",
                bg=self.entity_colors[entity_type],
                fg="black",
                font=("Arial", 7),
                relief=tk.RAISED,
                borderwidth=1
            )
            legend_item.pack(side=tk.LEFT, padx=1, pady=1)

    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 顶部工具栏
        self.create_toolbar(main_frame)
        
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # 数据浏览标签页
        self.create_data_browser_tab()
        
        # 可疑数据检查标签页
        self.create_suspicious_tab()
        
        # 手动标注标签页
        self.create_manual_annotation_tab()
        
        # 智能标注标签页（新增）
        self.create_smart_annotation_tab()

    def create_toolbar(self, parent):
        """创建工具栏"""
        toolbar = ttk.Frame(parent)
        toolbar.pack(fill=tk.X, pady=(0, 5))
        
        # 文件操作
        ttk.Button(toolbar, text="📁 加载数据", command=self.load_dataset).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="💾 保存数据", command=self.save_dataset).pack(side=tk.LEFT, padx=(0, 5))
        
        # 分隔符
        ttk.Separator(toolbar, orient='vertical').pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 模型状态显示
        if self.model_loaded:
            if self.use_improved_model and self.improved_inference:
                model_status = "🚀 改进模型已加载"
                model_color = "blue"
            else:
                model_status = "🤖 基础模型已加载"
                model_color = "green"
        else:
            model_status = "❌ 模型未加载"
            model_color = "red"
            
        self.model_status_label = tk.Label(toolbar, text=model_status, fg=model_color, font=("Arial", 9, "bold"))
        self.model_status_label.pack(side=tk.LEFT, padx=(0, 10))
        
        # 智能功能
        if self.model_loaded:
            ttk.Button(toolbar, text="🧠 智能预标注", command=self.auto_annotate_current_sample).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(toolbar, text="⚡ 批量标注", command=self.show_batch_annotation_tab).pack(side=tk.LEFT, padx=(0, 5))
        
        # 调试功能
        self.debug_var = tk.BooleanVar()
        debug_checkbox = tk.Checkbutton(toolbar, text="🔧 调试模式", variable=self.debug_var, command=self.toggle_debug_mode)
        debug_checkbox.pack(side=tk.LEFT, padx=(10, 5))
        
        # 模型切换功能
        if self.model_loaded and IMPROVED_MODEL_AVAILABLE:
            self.model_mode_var = tk.BooleanVar(value=self.use_improved_model)
            model_checkbox = tk.Checkbutton(toolbar, text="⚡ 使用改进模型", variable=self.model_mode_var, command=self.toggle_model_mode)
            model_checkbox.pack(side=tk.LEFT, padx=(5, 5))
        
        # 统计信息
        self.stats_label = ttk.Label(toolbar, text="数据集: 未加载")
        self.stats_label.pack(side=tk.RIGHT)

    def create_smart_annotation_tab(self):
        """创建智能标注标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="🧠 智能标注")
        
        # 主要内容区域
        main_content = ttk.Frame(frame)
        main_content.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题和说明
        title_frame = ttk.Frame(main_content)
        title_frame.pack(fill=tk.X, pady=(0, 15))
        
        title_label = tk.Label(title_frame, text="🤖 AI智能预标注", font=("Arial", 16, "bold"))
        title_label.pack()
        
        subtitle_label = tk.Label(
            title_frame, 
            text="基于训练好的中文NER模型，为文本自动生成实体标注",
            font=("Arial", 10),
            fg="gray"
        )
        subtitle_label.pack(pady=(5, 0))
        
        # 功能说明
        info_frame = ttk.LabelFrame(main_content, text="💡 功能介绍")
        info_frame.pack(fill=tk.X, pady=(0, 15))
        
        info_text = """
✨ 智能预标注功能：
• 🎯 使用已训练的中文NER模型自动识别实体
• 🏷️ 支持识别：人名(PER)、组织(ORG)、地点(LOC)、时间(TIME)、金钱(MONEY)、产品(PRODUCT)、事件(EVENT)
• ⚡ 快速处理，大幅提高标注效率
• 🔧 预标注结果可在"手动标注"页面进行调整

📊 模型性能：
• 整体F1分数：91.33%
• 人名识别：100% 准确率
• 机构识别：100% 准确率
• 时间识别：100% 准确率
• 地点识别：96.12% 准确率
        """
        
        info_label = tk.Label(info_frame, text=info_text, justify=tk.LEFT, font=("Arial", 9))
        info_label.pack(padx=10, pady=10)
        
        # 操作区域
        operation_frame = ttk.LabelFrame(main_content, text="🚀 智能标注操作")
        operation_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 当前样本预览
        preview_frame = ttk.Frame(operation_frame)
        preview_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(preview_frame, text="当前样本文本:").pack(anchor=tk.W)
        self.smart_preview_text = tk.Text(preview_frame, height=3, wrap=tk.WORD, font=("Arial", 11), state=tk.DISABLED)
        self.smart_preview_text.pack(fill=tk.X, pady=(5, 10))
        
        # 操作按钮
        button_frame = ttk.Frame(operation_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        if self.model_loaded:
            smart_annotate_btn = ttk.Button(
                button_frame, 
                text="🧠 对当前样本进行智能预标注",
                command=self.auto_annotate_current_sample
            )
            smart_annotate_btn.pack(pady=5)
            
            # 批量标注选项框
            batch_frame = ttk.LabelFrame(button_frame, text="批量智能预标注")
            batch_frame.pack(fill=tk.X, pady=10)
            
            # 批量范围选择
            range_frame = ttk.Frame(batch_frame)
            range_frame.pack(fill=tk.X, padx=5, pady=5)
            
            ttk.Label(range_frame, text="标注范围:").pack(side=tk.LEFT)
            
            self.batch_mode = tk.StringVar(value="all")
            ttk.Radiobutton(range_frame, text="全部样本", variable=self.batch_mode, value="all").pack(side=tk.LEFT, padx=5)
            ttk.Radiobutton(range_frame, text="未标注样本", variable=self.batch_mode, value="unlabeled").pack(side=tk.LEFT, padx=5)
            ttk.Radiobutton(range_frame, text="自定义范围", variable=self.batch_mode, value="custom").pack(side=tk.LEFT, padx=5)
            
            # 自定义范围输入
            custom_frame = ttk.Frame(batch_frame)
            custom_frame.pack(fill=tk.X, padx=5, pady=2)
            
            ttk.Label(custom_frame, text="从第").pack(side=tk.LEFT)
            self.start_index_entry = ttk.Entry(custom_frame, width=8)
            self.start_index_entry.pack(side=tk.LEFT, padx=2)
            ttk.Label(custom_frame, text="个到第").pack(side=tk.LEFT)
            self.end_index_entry = ttk.Entry(custom_frame, width=8)
            self.end_index_entry.pack(side=tk.LEFT, padx=2)
            ttk.Label(custom_frame, text="个样本").pack(side=tk.LEFT)
            
            # 批量操作按钮
            batch_btn_frame = ttk.Frame(batch_frame)
            batch_btn_frame.pack(fill=tk.X, padx=5, pady=5)
            
            batch_annotate_btn = ttk.Button(
                batch_btn_frame,
                text="⚡ 开始批量智能预标注",
                command=self.batch_auto_annotate
            )
            batch_annotate_btn.pack(side=tk.LEFT, padx=(0, 5))
            
            self.batch_stop_btn = ttk.Button(
                batch_btn_frame,
                text="⏹ 停止批量标注",
                command=self.stop_batch_annotation,
                state=tk.DISABLED
            )
            self.batch_stop_btn.pack(side=tk.LEFT)
            
            # 批量标注进度
            progress_frame = ttk.LabelFrame(operation_frame, text="批量标注进度")
            progress_frame.pack(fill=tk.X, padx=10, pady=(10, 0))
            
            self.batch_progress_label = ttk.Label(progress_frame, text="未开始批量标注")
            self.batch_progress_label.pack(pady=5)
            
            self.batch_progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
            self.batch_progress_bar.pack(fill=tk.X, padx=10, pady=5)
            
            self.batch_stats_label = ttk.Label(progress_frame, text="", font=("Arial", 9))
            self.batch_stats_label.pack(pady=2)
        else:
            no_model_label = tk.Label(
                button_frame,
                text="❌ 模型未加载，智能预标注功能不可用\n请确保模型文件存在于 ./model/best_model/ 或 ./model/chinese_ner_model/ 目录",
                fg="red",
                font=("Arial", 10)
            )
            no_model_label.pack(pady=20)
        
        # 使用提示
        tips_frame = ttk.LabelFrame(main_content, text="💡 使用提示")
        tips_frame.pack(fill=tk.X)
        
        tips_text = """
1. 在"数据浏览"页面选择要标注的样本
2. 点击"智能预标注"按钮，AI将自动识别实体
3. 在"手动标注"页面检查和修正预标注结果
4. 使用"保存数据"保存标注结果
        """
        
        tips_label = tk.Label(tips_frame, text=tips_text, justify=tk.LEFT, font=("Arial", 9))
        tips_label.pack(padx=10, pady=10)

    # 继续添加其他原有方法...
    def create_data_browser_tab(self):
        """创建数据浏览标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="数据浏览")
        
        # 分割为上下两部分
        paned = ttk.PanedWindow(frame, orient=tk.VERTICAL)
        paned.pack(fill=tk.BOTH, expand=True)
        
        # 上半部分：导航和控制
        top_frame = ttk.LabelFrame(paned, text="样本导航")
        paned.add(top_frame, weight=1)
        
        # 导航控件
        nav_frame = ttk.Frame(top_frame)
        nav_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 样本计数器
        self.sample_counter_label = ttk.Label(nav_frame, text="样本: 0/0")
        self.sample_counter_label.pack(side=tk.LEFT)
        
        # 导航按钮
        ttk.Button(nav_frame, text="⏮ 首个", command=self.first_sample).pack(side=tk.LEFT, padx=(20, 5))
        ttk.Button(nav_frame, text="⏪ 上一个", command=self.prev_sample).pack(side=tk.LEFT, padx=5)
        ttk.Button(nav_frame, text="⏩ 下一个", command=self.next_sample).pack(side=tk.LEFT, padx=5)
        ttk.Button(nav_frame, text="⏭ 最后", command=self.last_sample).pack(side=tk.LEFT, padx=(5, 20))
        
        # 跳转输入
        ttk.Label(nav_frame, text="跳转到:").pack(side=tk.LEFT, padx=(20, 5))
        self.goto_entry = ttk.Entry(nav_frame, width=8)
        self.goto_entry.pack(side=tk.LEFT, padx=5)
        ttk.Button(nav_frame, text="跳转", command=self.goto_sample).pack(side=tk.LEFT, padx=5)
        
        # 智能预标注按钮（在导航区域）
        if self.model_loaded:
            ttk.Button(nav_frame, text="🧠 智能预标注当前样本", command=self.auto_annotate_current_sample).pack(side=tk.RIGHT, padx=5)
        
        # 下半部分：数据显示
        bottom_frame = ttk.LabelFrame(paned, text="样本内容")
        paned.add(bottom_frame, weight=3)
        
        # 在数据显示区域也添加图例
        legend_frame = ttk.Frame(bottom_frame)
        legend_frame.pack(fill=tk.X, padx=5, pady=5)
        self.create_legend(legend_frame)

        # 文本显示区域
        text_frame = ttk.Frame(bottom_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 原始文本
        ttk.Label(text_frame, text="原始文本:").pack(anchor=tk.W)
        self.text_display = scrolledtext.ScrolledText(text_frame, height=4, wrap=tk.WORD, font=("Arial", 12))
        self.text_display.pack(fill=tk.X, pady=(0, 10))
        
        # 带标注的文本显示
        ttk.Label(text_frame, text="标注预览:").pack(anchor=tk.W)
        self.annotated_display = tk.Text(text_frame, height=8, wrap=tk.WORD, font=("Arial", 12))
        annotated_scroll = ttk.Scrollbar(text_frame, orient="vertical", command=self.annotated_display.yview)
        self.annotated_display.configure(yscrollcommand=annotated_scroll.set)
        self.annotated_display.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        annotated_scroll.pack(side=tk.RIGHT, fill=tk.Y)

    def create_suspicious_tab(self):
        """创建可疑数据检查标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="可疑数据检查")
        
        # 分割为左右两部分
        paned = ttk.PanedWindow(frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True)
        
        # 左侧：控制面板
        left_frame = ttk.LabelFrame(paned, text="检查控制")
        paned.add(left_frame, weight=1)
        
        # 检查按钮
        check_frame = ttk.Frame(left_frame)
        check_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(check_frame, text="🔍 开始检查", command=self.check_suspicious_data).pack(fill=tk.X, pady=2)
        ttk.Button(check_frame, text="📊 生成报告", command=self.generate_report).pack(fill=tk.X, pady=2)
        
        # 检查选项
        options_frame = ttk.LabelFrame(left_frame, text="检查选项")
        options_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.check_encoding = tk.BooleanVar(value=True)
        self.check_length = tk.BooleanVar(value=True)
        self.check_labels = tk.BooleanVar(value=True)
        self.check_consistency = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(options_frame, text="编码问题", variable=self.check_encoding).pack(anchor=tk.W)
        ttk.Checkbutton(options_frame, text="长度异常", variable=self.check_length).pack(anchor=tk.W)
        ttk.Checkbutton(options_frame, text="标签错误", variable=self.check_labels).pack(anchor=tk.W)
        ttk.Checkbutton(options_frame, text="一致性检查", variable=self.check_consistency).pack(anchor=tk.W)
        
        # 可疑数据导航
        nav_frame = ttk.LabelFrame(left_frame, text="可疑数据导航")
        nav_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.suspicious_counter_label = ttk.Label(nav_frame, text="可疑样本: 0/0")
        self.suspicious_counter_label.pack()
        
        suspicious_btn_frame = ttk.Frame(nav_frame)
        suspicious_btn_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(suspicious_btn_frame, text="⏪ 上一个", command=self.prev_suspicious).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(suspicious_btn_frame, text="⏩ 下一个", command=self.next_suspicious).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Button(nav_frame, text="📝 跳转到标注", command=self.goto_annotation).pack(fill=tk.X, pady=2)
        
        # 右侧：可疑数据显示
        right_frame = ttk.LabelFrame(paned, text="可疑数据详情")
        paned.add(right_frame, weight=2)
        
        # 问题描述
        ttk.Label(right_frame, text="问题描述:").pack(anchor=tk.W, padx=5, pady=(5, 0))
        self.problem_display = scrolledtext.ScrolledText(right_frame, height=3, wrap=tk.WORD)
        self.problem_display.pack(fill=tk.X, padx=5, pady=5)
        
        # 样本内容
        ttk.Label(right_frame, text="样本内容:").pack(anchor=tk.W, padx=5)
        self.suspicious_content_display = scrolledtext.ScrolledText(right_frame, height=8, wrap=tk.WORD, font=("Arial", 12))
        self.suspicious_content_display.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def create_manual_annotation_tab(self):
        """创建手动标注标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="手动标注")
        
        # 分割为左右两部分
        paned = ttk.PanedWindow(frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True)
        
        # 左侧：句子编辑
        left_frame = ttk.LabelFrame(paned, text="句子编辑")
        paned.add(left_frame, weight=2)
        
        # 句子输入
        ttk.Label(left_frame, text="句子:").pack(anchor=tk.W, padx=5, pady=(5, 0))
        self.edit_sentence_entry = tk.Text(left_frame, height=3, wrap=tk.WORD, font=("Arial", 12))
        self.edit_sentence_entry.pack(fill=tk.X, padx=5, pady=5)
        
        # 字符标签编辑
        ttk.Label(left_frame, text="字符标签 (每行一个字符和标签，用制表符分隔):").pack(anchor=tk.W, padx=5)
        self.edit_tags_text = scrolledtext.ScrolledText(left_frame, wrap=tk.NONE, font=("Courier", 10))
        self.edit_tags_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 编辑按钮
        edit_buttons = ttk.Frame(left_frame)
        edit_buttons.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(edit_buttons, text="加载当前样本", command=self.load_current_sample_for_edit).pack(side=tk.LEFT, padx=(0, 5))
        
        # 智能预标注按钮（在手动标注页面）
        if self.model_loaded:
            ttk.Button(edit_buttons, text="🧠 智能预标注", command=self.smart_annotate_and_load).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(edit_buttons, text="应用修改", command=self.apply_edit).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(edit_buttons, text="重置", command=self.reset_edit).pack(side=tk.LEFT, padx=(0, 5))
        
        # 右侧：实体类型参考
        right_frame = ttk.LabelFrame(paned, text="标注参考")
        paned.add(right_frame, weight=1)

        # 在标注参考页面也添加图例
        legend_frame_manual = ttk.Frame(right_frame)
        legend_frame_manual.pack(fill=tk.X, padx=5, pady=5)
        self.create_compact_legend(legend_frame_manual)

        # 实体类型说明
        entity_info = """实体类型说明:
        
PER - 人名
格式: B-PER, I-PER
示例: 张三 -> 张(B-PER) 三(I-PER)

ORG - 组织机构
格式: B-ORG, I-ORG
示例: 清华大学 -> 清(B-ORG) 华(I-ORG) 大(I-ORG) 学(I-ORG)

LOC - 地点位置
格式: B-LOC, I-LOC
示例: 北京市 -> 北(B-LOC) 京(I-LOC) 市(I-LOC)

TIME - 时间表达
格式: B-TIME, I-TIME
示例: 2023年 -> 2(B-TIME) 0(I-TIME) 2(I-TIME) 3(I-TIME) 年(I-TIME)

MONEY - 金钱数额
格式: B-MONEY, I-MONEY
示例: 100元 -> 1(B-MONEY) 0(I-MONEY) 0(I-MONEY) 元(I-MONEY)

PRODUCT - 产品名称
格式: B-PRODUCT, I-PRODUCT
示例: iPhone -> i(B-PRODUCT) P(I-PRODUCT) h(I-PRODUCT) o(I-PRODUCT) n(I-PRODUCT) e(I-PRODUCT)

EVENT - 事件活动
格式: B-EVENT, I-EVENT
示例: 春节 -> 春(B-EVENT) 节(I-EVENT)

标注规则:
- B-标签表示实体开始
- I-标签表示实体内部
- O表示非实体
- 每个字符必须有对应标签
        """
        
        info_text = scrolledtext.ScrolledText(right_frame, wrap=tk.WORD, font=("Arial", 9))
        info_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        info_text.insert(tk.END, entity_info)
        info_text.config(state=tk.DISABLED)

    def smart_annotate_and_load(self):
        """智能预标注并加载到编辑器"""
        if not self.model_loaded:
            messagebox.showwarning("模型未加载", "智能预标注功能不可用")
            return
            
        # 先进行智能预标注
        self.auto_annotate_current_sample()
        
        # 然后加载到编辑器
        self.root.after(1000, self.load_current_sample_for_edit)  # 延迟加载以等待预标注完成

    def update_smart_preview(self):
        """更新智能标注页面的文本预览"""
        self.smart_preview_text.config(state=tk.NORMAL)
        self.smart_preview_text.delete(1.0, tk.END)
        
        if self.dataset and 0 <= self.current_index < len(self.dataset):
            sample = self.dataset[self.current_index]
            text = sample.get('text', '无文本')
            self.smart_preview_text.insert(tk.END, text)
        else:
            self.smart_preview_text.insert(tk.END, "请先加载数据集并选择样本")
            
        self.smart_preview_text.config(state=tk.DISABLED)

    # 以下是原有方法的实现（需要从原文件复制）
    def load_dataset(self):
        """加载数据集"""
        filename = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    self.dataset = json.load(f)
                
                self.current_index = 0
                self.modified_samples.clear()
                self.suspicious_cases.clear()
                self.current_suspicious_index = 0
                
                messagebox.showinfo("成功", f"成功加载 {len(self.dataset)} 个样本")
                self.update_stats()
                self.display_current_sample()
                self.update_smart_preview()
                
            except Exception as e:
                messagebox.showerror("错误", f"加载数据失败:\n{e}")

    def save_dataset(self):
        """保存数据集"""
        if not self.dataset:
            messagebox.showwarning("警告", "没有数据可保存")
            return
            
        filename = filedialog.asksaveasfilename(
            title="保存数据文件",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.dataset, f, ensure_ascii=False, indent=2)
                
                messagebox.showinfo("成功", "数据保存成功")
                
            except Exception as e:
                messagebox.showerror("错误", f"保存数据失败:\n{e}")

    def update_stats(self):
        """更新统计信息"""
        if self.dataset:
            total = len(self.dataset)
            modified = len(self.modified_samples)
            self.stats_label.config(text=f"数据集: {total} 个样本 | 已修改: {modified}")
        else:
            self.stats_label.config(text="数据集: 未加载")

    def display_current_sample(self):
        """显示当前样本"""
        if not self.dataset:
            return
            
        if 0 <= self.current_index < len(self.dataset):
            sample = self.dataset[self.current_index]
            
            # 更新计数器
            self.sample_counter_label.config(text=f"样本: {self.current_index + 1}/{len(self.dataset)}")
            
            # 显示原始文本
            self.text_display.delete(1.0, tk.END)
            self.text_display.insert(tk.END, sample.get('text', ''))
            
            # 显示带标注的文本
            self.display_annotated_text(sample)
            
            # 更新智能标注预览
            self.update_smart_preview()

    def display_annotated_text(self, sample):
        """显示带标注的文本"""
        self.annotated_display.delete(1.0, tk.END)
        
        text = sample.get('text', '')
        labels = sample.get('labels', [])
        
        if len(text) != len(labels):
            self.annotated_display.insert(tk.END, f"⚠️ 文本长度({len(text)})与标签长度({len(labels)})不匹配\n\n")
            self.annotated_display.insert(tk.END, f"文本: {text}\n")
            self.annotated_display.insert(tk.END, f"标签: {labels}")
            return
        
        # 先插入文本
        self.annotated_display.insert(1.0, text)
        
        # 创建实体颜色标签配置
        for entity_type, color in self.entity_colors.items():
            self.annotated_display.tag_configure(f"entity_{entity_type}", background=color)
        
        # 然后设置标签颜色
        current_entity = None
        current_start = None
        
        for i, (char, label) in enumerate(zip(text, labels)):
            if label.startswith('B-'):
                # 结束之前的实体
                if current_entity:
                    self.annotated_display.tag_add(f"entity_{current_entity}", 
                                                 f"1.{current_start}", f"1.{i}")
                
                # 开始新实体
                current_entity = label[2:]
                current_start = i
                
            elif label.startswith('I-'):
                # 继续当前实体
                if not current_entity or current_entity != label[2:]:
                    # 标签不一致，结束之前的实体并开始新实体
                    if current_entity:
                        self.annotated_display.tag_add(f"entity_{current_entity}", 
                                                     f"1.{current_start}", f"1.{i}")
                    current_entity = label[2:]
                    current_start = i
                    
            else:  # O 或其他
                # 结束当前实体
                if current_entity:
                    self.annotated_display.tag_add(f"entity_{current_entity}", 
                                                 f"1.{current_start}", f"1.{i}")
                    current_entity = None
                    current_start = None
        
        # 结束最后的实体
        if current_entity:
            self.annotated_display.tag_add(f"entity_{current_entity}", 
                                         f"1.{current_start}", f"1.{len(text)}")
        
        # 添加调试信息（可选）
        if hasattr(self, 'debug_mode') and self.debug_mode:
            entity_count = sum(1 for label in labels if label.startswith('B-'))
            self.annotated_display.insert(tk.END, f"\n\n[调试] 实体数量: {entity_count}")
            self.annotated_display.insert(tk.END, f" | 文本长度: {len(text)} | 标签长度: {len(labels)}")
            
            # 显示实体类型统计
            entity_types = {}
            for label in labels:
                if label.startswith('B-'):
                    entity_type = label[2:]
                    entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
            if entity_types:
                entity_info = ", ".join([f"{k}:{v}" for k, v in entity_types.items()])
                self.annotated_display.insert(tk.END, f" | 实体类型: {entity_info}")

    # 导航方法
    def first_sample(self):
        """跳转到第一个样本"""
        if self.dataset:
            self.current_index = 0
            self.display_current_sample()

    def last_sample(self):
        """跳转到最后一个样本"""
        if self.dataset:
            self.current_index = len(self.dataset) - 1
            self.display_current_sample()

    def prev_sample(self):
        """上一个样本"""
        if self.dataset and self.current_index > 0:
            self.current_index -= 1
            self.display_current_sample()

    def next_sample(self):
        """下一个样本"""
        if self.dataset and self.current_index < len(self.dataset) - 1:
            self.current_index += 1
            self.display_current_sample()

    def goto_sample(self):
        """跳转到指定样本"""
        try:
            index = int(self.goto_entry.get()) - 1
            if 0 <= index < len(self.dataset):
                self.current_index = index
                self.display_current_sample()
            else:
                messagebox.showwarning("警告", "样本索引超出范围")
        except ValueError:
            messagebox.showwarning("警告", "请输入有效的数字")

    # 手动标注相关方法
    def load_current_sample_for_edit(self):
        """加载当前样本到编辑器"""
        if not self.dataset or not (0 <= self.current_index < len(self.dataset)):
            return
            
        sample = self.dataset[self.current_index]
        text = sample.get('text', '')
        labels = sample.get('labels', [])
        
        # 加载句子
        self.edit_sentence_entry.delete(1.0, tk.END)
        self.edit_sentence_entry.insert(tk.END, text)
        
        # 加载标签
        self.edit_tags_text.delete(1.0, tk.END)
        
        if len(text) == len(labels):
            for char, label in zip(text, labels):
                self.edit_tags_text.insert(tk.END, f"{char}\t{label}\n")
        else:
            # 如果长度不匹配，显示警告并尽量对齐
            self.edit_tags_text.insert(tk.END, f"# 警告: 文本长度({len(text)})与标签长度({len(labels)})不匹配\n")
            max_len = max(len(text), len(labels))
            for i in range(max_len):
                char = text[i] if i < len(text) else '?'
                label = labels[i] if i < len(labels) else 'O'
                self.edit_tags_text.insert(tk.END, f"{char}\t{label}\n")

    def apply_edit(self):
        """应用编辑修改"""
        if not self.dataset or not (0 <= self.current_index < len(self.dataset)):
            return
            
        try:
            # 获取编辑的句子
            new_text = self.edit_sentence_entry.get(1.0, tk.END).strip()
            
            # 解析标签
            tags_content = self.edit_tags_text.get(1.0, tk.END).strip()
            lines = [line for line in tags_content.split('\n') if line.strip() and not line.startswith('#')]
            
            chars = []
            labels = []
            
            for line in lines:
                parts = line.split('\t')
                if len(parts) >= 2:
                    chars.append(parts[0])
                    labels.append(parts[1])
            
            # 验证
            reconstructed_text = ''.join(chars)
            if reconstructed_text != new_text:
                if not messagebox.askyesno("确认", "重构的文本与输入文本不一致，是否继续？"):
                    return
            
            # 应用修改
            self.dataset[self.current_index]['text'] = new_text
            self.dataset[self.current_index]['labels'] = labels
            
            # 标记为已修改
            self.modified_samples.add(self.current_index)
            
            # 刷新显示
            self.display_current_sample()
            self.update_stats()
            
            messagebox.showinfo("成功", "修改已应用")
            
        except Exception as e:
            messagebox.showerror("错误", f"应用修改失败:\n{e}")

    def reset_edit(self):
        """重置编辑"""
        self.load_current_sample_for_edit()

    # 可疑数据检查相关方法
    def check_suspicious_data(self):
        """检查可疑数据"""
        if not self.dataset:
            messagebox.showwarning("警告", "请先加载数据集")
            return
            
        self.suspicious_cases.clear()
        
        for i, sample in enumerate(self.dataset):
            problems = []
            text = sample.get('text', '')
            labels = sample.get('labels', [])
            
            # 编码检查
            if self.check_encoding.get():
                try:
                    text.encode('utf-8')
                except UnicodeEncodeError:
                    problems.append("文本包含无法编码的字符")
            
            # 长度检查
            if self.check_length.get():
                if len(text) != len(labels):
                    problems.append(f"文本长度({len(text)})与标签长度({len(labels)})不匹配")
                
                if len(text) == 0:
                    problems.append("文本为空")
                    
                if len(text) > 500:
                    problems.append(f"文本过长({len(text)}字符)")
            
            # 标签检查
            if self.check_labels.get():
                valid_labels = {'O', 'B-PER', 'I-PER', 'B-ORG', 'I-ORG', 'B-LOC', 'I-LOC', 
                              'B-TIME', 'I-TIME', 'B-MONEY', 'I-MONEY', 'B-PRODUCT', 'I-PRODUCT',
                              'B-EVENT', 'I-EVENT'}
                
                for j, label in enumerate(labels):
                    if label not in valid_labels:
                        problems.append(f"位置{j}包含无效标签: {label}")
                        break
            
            # 一致性检查
            if self.check_consistency.get():
                prev_label = None
                for j, label in enumerate(labels):
                    if label.startswith('I-') and (prev_label is None or 
                        not (prev_label.startswith('B-') or prev_label.startswith('I-')) or
                        prev_label.split('-', 1)[1] != label.split('-', 1)[1]):
                        problems.append(f"位置{j}的I-标签前没有对应的B-标签")
                        break
                    prev_label = label
            
            if problems:
                self.suspicious_cases.append({
                    'index': i,
                    'problems': problems,
                    'sample': sample
                })
        
        self.current_suspicious_index = 0
        self.display_suspicious_sample()
        
        messagebox.showinfo("检查完成", f"发现 {len(self.suspicious_cases)} 个可疑样本")

    def display_suspicious_sample(self):
        """显示可疑样本"""
        if not self.suspicious_cases:
            self.suspicious_counter_label.config(text="可疑样本: 0/0")
            self.problem_display.delete(1.0, tk.END)
            self.suspicious_content_display.delete(1.0, tk.END)
            return
            
        if 0 <= self.current_suspicious_index < len(self.suspicious_cases):
            case = self.suspicious_cases[self.current_suspicious_index]
            
            # 更新计数器
            self.suspicious_counter_label.config(
                text=f"可疑样本: {self.current_suspicious_index + 1}/{len(self.suspicious_cases)}"
            )
            
            # 显示问题
            self.problem_display.delete(1.0, tk.END)
            problems_text = f"样本索引: {case['index'] + 1}\n问题:\n"
            for i, problem in enumerate(case['problems'], 1):
                problems_text += f"{i}. {problem}\n"
            self.problem_display.insert(tk.END, problems_text)
            
            # 显示样本内容
            self.suspicious_content_display.delete(1.0, tk.END)
            sample = case['sample']
            content = f"文本: {sample.get('text', '')}\n\n"
            content += f"标签: {sample.get('labels', [])}"
            self.suspicious_content_display.insert(tk.END, content)

    def prev_suspicious(self):
        """上一个可疑样本"""
        if self.suspicious_cases and self.current_suspicious_index > 0:
            self.current_suspicious_index -= 1
            self.display_suspicious_sample()

    def next_suspicious(self):
        """下一个可疑样本"""
        if self.suspicious_cases and self.current_suspicious_index < len(self.suspicious_cases) - 1:
            self.current_suspicious_index += 1
            self.display_suspicious_sample()

    def goto_annotation(self):
        """跳转到当前可疑样本的标注页面"""
        if not self.suspicious_cases or not (0 <= self.current_suspicious_index < len(self.suspicious_cases)):
            return
            
        case = self.suspicious_cases[self.current_suspicious_index]
        self.current_index = case['index']
        self.display_current_sample()
        self.notebook.select(2)  # 切换到手动标注标签页

    def generate_report(self):
        """生成检查报告"""
        if not self.suspicious_cases:
            messagebox.showinfo("提示", "没有可疑数据，无需生成报告")
            return
            
        filename = filedialog.asksaveasfilename(
            title="保存检查报告",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("NER数据质量检查报告\n")
                    f.write("=" * 50 + "\n\n")
                    f.write(f"总样本数: {len(self.dataset)}\n")
                    f.write(f"可疑样本数: {len(self.suspicious_cases)}\n")
                    f.write(f"可疑比例: {len(self.suspicious_cases)/len(self.dataset)*100:.2f}%\n\n")
                    
                    for i, case in enumerate(self.suspicious_cases, 1):
                        f.write(f"可疑样本 {i}:\n")
                        f.write(f"  索引: {case['index'] + 1}\n")
                        f.write(f"  问题:\n")
                        for j, problem in enumerate(case['problems'], 1):
                            f.write(f"    {j}. {problem}\n")
                        f.write(f"  文本: {case['sample'].get('text', '')}\n")
                        f.write(f"  标签数量: {len(case['sample'].get('labels', []))}\n")
                        f.write("-" * 30 + "\n\n")
                
                messagebox.showinfo("成功", "报告生成成功")
                
            except Exception as e:
                messagebox.showerror("错误", f"生成报告失败:\n{e}")


def main():
    """主函数"""
    root = tk.Tk()
    app = EnhancedNERAnnotationUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()