#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NER数据生成器 - 生成字符级别的BIO标注数据
支持多种实体类型，生成复杂的中文句子用于模型训练
"""

import random
import json
from typing import List, Tuple, Dict
from datetime import datetime, timedelta
import re

class NERDataGenerator:
    def __init__(self):
        # 定义实体类型
        self.entity_types = {
            'PER': '人名',
            'ORG': '组织机构',
            'LOC': '地点',
            'TIME': '时间',
            'MONEY': '金额',
            'PRODUCT': '产品',
            'EVENT': '事件'
        }
        
        # 人名数据
        self.surnames = ['张', '李', '王', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗']
        self.given_names = ['伟', '芳', '娜', '秀英', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '娟', '涛', '明', '超', '秀兰', '霞']
        
        # 组织机构
        self.organizations = [
            '北京大学', '清华大学', '中国科学院', '阿里巴巴集团', '腾讯科技', '百度公司',
            '华为技术有限公司', '中国移动', '工商银行', '建设银行', '中国石油', '国家电网',
            '中央电视台', '人民日报社', '新华社', '中国银行', '农业银行', '招商银行'
        ]
        
        # 地点
        self.locations = [
            '北京市', '上海市', '广州市', '深圳市', '杭州市', '南京市', '武汉市', '成都市',
            '西安市', '重庆市', '天津市', '苏州市', '长沙市', '郑州市', '青岛市', '大连市',
            '中关村', '陆家嘴', '珠江新城', '前海', '滨江区', '浦东新区', '海淀区', '朝阳区'
        ]
        
        # 产品名称
        self.products = [
            'iPhone 15', 'MacBook Pro', '小米手机', '华为Mate60', 'Tesla Model 3',
            '比亚迪汉', '理想ONE', '蔚来ES8', 'ChatGPT', '文心一言', '通义千问',
            '微信', '支付宝', '抖音', '快手', '淘宝', '京东', '美团', '滴滴出行'
        ]
        
        # 事件
        self.events = [
            '春节联欢晚会', '世界杯', '奥运会', '进博会', '双十一购物节', '618购物节',
            '开发者大会', '新品发布会', '股东大会', '年度总结会', '技术峰会', '创新大赛'
        ]
        
        # 句子模板
        self.sentence_templates = [
            "{person}在{location}的{organization}工作",
            "{person}于{time}在{location}参加了{event}",
            "{organization}发布了新产品{product}，售价{money}",
            "{person}计划在{time}前往{location}出差",
            "{organization}将在{time}举办{event}",
            "{product}在{location}的销量突破{money}",
            "{person}和{person}共同创立了{organization}",
            "{time}，{organization}在{location}召开{event}",
            "{person}使用{product}完成了工作任务",
            "{organization}投资{money}研发{product}"
        ]
        
    def generate_person_name(self) -> str:
        """生成人名"""
        surname = random.choice(self.surnames)
        given_name = random.choice(self.given_names)
        if random.random() < 0.3:  # 30%概率生成两字名
            given_name += random.choice(self.given_names)
        return surname + given_name
        
    def generate_time(self) -> str:
        """生成时间表达"""
        time_formats = [
            lambda: f"{random.randint(2020, 2024)}年{random.randint(1, 12)}月{random.randint(1, 28)}日",
            lambda: f"{random.randint(1, 12)}月{random.randint(1, 28)}日",
            lambda: f"今年{random.randint(1, 12)}月",
            lambda: f"明年{random.randint(1, 12)}月",
            lambda: f"{random.randint(2020, 2024)}年",
            lambda: f"上周{random.choice(['一', '二', '三', '四', '五', '六', '日'])}",
            lambda: f"下个月{random.randint(1, 28)}号"
        ]
        return random.choice(time_formats)()
        
    def generate_money(self) -> str:
        """生成金额表达"""
        money_formats = [
            lambda: f"{random.randint(1, 999)}万元",
            lambda: f"{random.randint(1, 99)}亿元",
            lambda: f"{random.randint(100, 9999)}元",
            lambda: f"{random.randint(1, 99)}.{random.randint(10, 99)}万元",
            lambda: f"{random.randint(1, 999)}万美元",
            lambda: f"{random.randint(1, 99)}亿美元"
        ]
        return random.choice(money_formats)()
        
    def generate_sentence(self) -> str:
        """生成一个句子"""
        template = random.choice(self.sentence_templates)
        
        # 准备实体数据
        entities = {
            'person': self.generate_person_name(),
            'location': random.choice(self.locations),
            'organization': random.choice(self.organizations),
            'time': self.generate_time(),
            'money': self.generate_money(),
            'product': random.choice(self.products),
            'event': random.choice(self.events)
        }
        
        # 如果模板需要多个person，生成不同的人名
        person_count = template.count('{person}')
        if person_count > 1:
            persons = [self.generate_person_name() for _ in range(person_count)]
            template_parts = template.split('{person}')
            sentence = template_parts[0]
            for i, person in enumerate(persons):
                sentence += person
                if i + 1 < len(template_parts):
                    sentence += template_parts[i + 1]
            template = sentence
            
        # 替换其他占位符
        for key, value in entities.items():
            if key != 'person':
                template = template.replace(f'{{{key}}}', value)
                
        return template
        
    def char_level_bio_tagging(self, sentence: str) -> List[Tuple[str, str]]:
        """对句子进行字符级别的BIO标注"""
        chars = list(sentence)
        tags = ['O'] * len(chars)
        
        # 定义实体识别规则
        entity_patterns = {
            'PER': self.surnames + [name for name in self.given_names],
            'ORG': self.organizations,
            'LOC': self.locations,
            'PRODUCT': self.products,
            'EVENT': self.events
        }
        
        # 标注实体
        for entity_type, entities in entity_patterns.items():
            for entity in entities:
                start = 0
                while True:
                    pos = sentence.find(entity, start)
                    if pos == -1:
                        break
                    
                    # 标注实体
                    for i, char in enumerate(entity):
                        if i == 0:
                            tags[pos + i] = f'B-{entity_type}'
                        else:
                            tags[pos + i] = f'I-{entity_type}'
                    
                    start = pos + 1
        
        # 时间和金额的正则匹配
        time_pattern = r'\d{4}年\d{1,2}月\d{1,2}日|\d{1,2}月\d{1,2}日|今年\d{1,2}月|明年\d{1,2}月|\d{4}年|上周[一二三四五六日]|下个月\d{1,2}号'
        money_pattern = r'\d+万元|\d+亿元|\d+元|\d+\.\d+万元|\d+万美元|\d+亿美元'
        
        for pattern, entity_type in [(time_pattern, 'TIME'), (money_pattern, 'MONEY')]:
            for match in re.finditer(pattern, sentence):
                start, end = match.span()
                for i in range(start, end):
                    if i == start:
                        tags[i] = f'B-{entity_type}'
                    else:
                        tags[i] = f'I-{entity_type}'
        
        return list(zip(chars, tags))

    def generate_complex_sentence(self) -> str:
        """生成更复杂的句子"""
        complex_templates = [
            "{person}作为{organization}的代表，将在{time}前往{location}参加{event}，预计投资{money}用于{product}的研发。",
            "据{organization}消息，{person}在{time}成功完成了{product}项目，为公司节省了{money}的成本。",
            "{time}，{person}和团队在{location}发布了{product}，{organization}股价上涨，市值增加{money}。",
            "{organization}宣布，{person}将担任新职务，负责{product}在{location}的推广，预算为{money}。",
            "在{event}上，{person}代表{organization}展示了{product}，获得了{location}政府{money}的资助。"
        ]

        template = random.choice(complex_templates)

        # 生成实体
        entities = {
            'person': self.generate_person_name(),
            'location': random.choice(self.locations),
            'organization': random.choice(self.organizations),
            'time': self.generate_time(),
            'money': self.generate_money(),
            'product': random.choice(self.products),
            'event': random.choice(self.events)
        }

        # 替换占位符
        for key, value in entities.items():
            template = template.replace(f'{{{key}}}', value)

        return template

    def generate_dataset(self, num_samples: int = 20000) -> List[Dict]:
        """生成指定数量的NER数据集"""
        dataset = []

        print(f"开始生成{num_samples}条NER数据...")

        for i in range(num_samples):
            if i % 1000 == 0:
                print(f"已生成 {i}/{num_samples} 条数据")

            # 70%概率生成复杂句子，30%概率生成简单句子
            if random.random() < 0.7:
                sentence = self.generate_complex_sentence()
            else:
                sentence = self.generate_sentence()

            # 进行BIO标注
            char_tags = self.char_level_bio_tagging(sentence)

            # 构造数据样本
            sample = {
                'id': i + 1,
                'sentence': sentence,
                'chars': [char for char, tag in char_tags],
                'tags': [tag for char, tag in char_tags],
                'length': len(sentence)
            }

            dataset.append(sample)

        print(f"数据生成完成！共生成{len(dataset)}条数据")
        return dataset

    def save_dataset(self, dataset: List[Dict], output_format: str = 'json'):
        """保存数据集到文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        if output_format == 'json':
            filename = f"ner_dataset_{timestamp}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(dataset, f, ensure_ascii=False, indent=2)
            print(f"数据集已保存到: {filename}")

        elif output_format == 'conll':
            filename = f"ner_dataset_{timestamp}.conll"
            with open(filename, 'w', encoding='utf-8') as f:
                for sample in dataset:
                    f.write(f"# Sentence {sample['id']}: {sample['sentence']}\n")
                    for char, tag in zip(sample['chars'], sample['tags']):
                        f.write(f"{char}\t{tag}\n")
                    f.write("\n")
            print(f"CoNLL格式数据集已保存到: {filename}")

        elif output_format == 'bio':
            filename = f"ner_dataset_{timestamp}.bio"
            with open(filename, 'w', encoding='utf-8') as f:
                for sample in dataset:
                    chars_line = " ".join(sample['chars'])
                    tags_line = " ".join(sample['tags'])
                    f.write(f"{chars_line}\n{tags_line}\n\n")
            print(f"BIO格式数据集已保存到: {filename}")

    def analyze_dataset(self, dataset: List[Dict]):
        """分析数据集统计信息"""
        total_samples = len(dataset)
        total_chars = sum(sample['length'] for sample in dataset)
        avg_length = total_chars / total_samples

        # 统计实体类型分布
        entity_counts = {}
        for sample in dataset:
            for tag in sample['tags']:
                if tag != 'O':
                    entity_type = tag.split('-')[1] if '-' in tag else tag
                    entity_counts[entity_type] = entity_counts.get(entity_type, 0) + 1

        print("\n=== 数据集统计信息 ===")
        print(f"总样本数: {total_samples}")
        print(f"总字符数: {total_chars}")
        print(f"平均句子长度: {avg_length:.2f} 字符")
        print(f"实体类型分布:")
        for entity_type, count in sorted(entity_counts.items()):
            print(f"  {entity_type}: {count} 个")


def main():
    """主函数"""
    generator = NERDataGenerator()

    # 生成数据集
    dataset = generator.generate_dataset(num_samples=20000)

    # 分析数据集
    generator.analyze_dataset(dataset)

    # 保存数据集（多种格式）
    generator.save_dataset(dataset, 'json')
    generator.save_dataset(dataset, 'conll')
    generator.save_dataset(dataset, 'bio')

    # 显示几个样本
    print("\n=== 数据样本展示 ===")
    for i in range(3):
        sample = dataset[i]
        print(f"\n样本 {sample['id']}:")
        print(f"句子: {sample['sentence']}")
        print("字符-标签对:")
        for char, tag in zip(sample['chars'], sample['tags']):
            if tag != 'O':
                print(f"  {char}: {tag}")


if __name__ == "__main__":
    main()
