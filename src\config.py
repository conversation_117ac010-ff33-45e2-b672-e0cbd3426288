#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NER数据生成器配置文件
"""

# 数据生成配置
DATASET_CONFIG = {
    'num_samples': 20000,           # 生成样本数量
    'complex_sentence_ratio': 0.7,  # 复杂句子比例
    'output_formats': ['json', 'conll', 'bio'],  # 输出格式
    'random_seed': 42               # 随机种子
}

# 实体类型定义
ENTITY_TYPES = {
    'PER': '人名',
    'ORG': '组织机构',
    'LOC': '地点',
    'TIME': '时间',
    'MONEY': '金额',
    'PRODUCT': '产品',
    'EVENT': '事件'
}

# 输出路径配置
PATHS = {
    'data_dir': 'data',
    'output_dir': 'data',
    'docs_dir': 'docs'
}
