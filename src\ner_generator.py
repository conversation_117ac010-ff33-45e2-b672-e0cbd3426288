#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NER数据生成器 - 核心模块
生成字符级别的BIO标注数据用于中文NER模型训练
"""

import random
import json
import re
from typing import List, Tuple, Dict
from datetime import datetime

class NERGenerator:
    """NER数据生成器核心类"""
    
    def __init__(self, config=None):
        """初始化生成器"""
        self.config = config or self._default_config()
        self._init_vocab()
        
        # 设置随机种子
        if 'random_seed' in self.config:
            random.seed(self.config['random_seed'])
    
    def _default_config(self) -> Dict:
        """默认配置"""
        return {
            'num_samples': 20000,
            'complex_sentence_ratio': 0.7,
            'output_formats': ['json', 'conll', 'bio'],
            'random_seed': 42
        }
    
    def _init_vocab(self):
        """初始化词汇库"""
        # 人名
        self.surnames = ['张', '李', '王', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林']
        self.given_names = ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '娟', '涛', '明']

        # 常见介词和连词，需要排除
        self.stop_chars = ['于', '在', '和', '与', '及', '或', '但', '而', '的', '了', '是', '有', '为', '将', '被', '把']
        
        # 组织机构
        self.organizations = [
            '北京大学', '清华大学', '阿里巴巴集团', '腾讯科技', '百度公司', '华为技术有限公司',
            '中国移动', '工商银行', '建设银行', '中央电视台', '新华社'
        ]
        
        # 地点
        self.locations = [
            '北京市', '上海市', '广州市', '深圳市', '杭州市', '南京市', '武汉市', '成都市',
            '中关村', '陆家嘴', '珠江新城', '海淀区', '朝阳区'
        ]
        
        # 产品
        self.products = [
            'iPhone 15', 'MacBook Pro', '小米手机', '华为Mate60', '微信', '支付宝', '抖音', '淘宝'
        ]
        
        # 事件
        self.events = [
            '春节联欢晚会', '世界杯', '奥运会', '进博会', '双十一购物节', '开发者大会', '新品发布会'
        ]
        
        # 句子模板
        self.simple_templates = [
            "{person}在{location}的{organization}工作",
            "{person}于{time}在{location}参加了{event}",
            "{organization}发布了新产品{product}，售价{money}",
            "{organization}将在{time}举办{event}"
        ]
        
        self.complex_templates = [
            "{person}作为{organization}的代表，将在{time}前往{location}参加{event}，预计投资{money}用于{product}的研发。",
            "据{organization}消息，{person}在{time}成功完成了{product}项目，为公司节省了{money}的成本。",
            "{organization}宣布，{person}将担任新职务，负责{product}在{location}的推广，预算为{money}。"
        ]
    
    def generate_person_name(self) -> str:
        """生成人名"""
        surname = random.choice(self.surnames)
        given_name = random.choice(self.given_names)
        if random.random() < 0.3:
            given_name += random.choice(self.given_names)
        return surname + given_name
    
    def generate_time(self) -> str:
        """生成时间表达"""
        formats = [
            f"{random.randint(2020, 2024)}年{random.randint(1, 12)}月{random.randint(1, 28)}日",
            f"{random.randint(1, 12)}月{random.randint(1, 28)}日",
            f"今年{random.randint(1, 12)}月",
            f"{random.randint(2020, 2024)}年"
        ]
        return random.choice(formats)
    
    def generate_money(self) -> str:
        """生成金额表达"""
        formats = [
            f"{random.randint(1, 999)}万元",
            f"{random.randint(1, 99)}亿元",
            f"{random.randint(100, 9999)}元"
        ]
        return random.choice(formats)
    
    def generate_sentence(self, complexity='simple') -> str:
        """生成句子"""
        templates = self.complex_templates if complexity == 'complex' else self.simple_templates
        template = random.choice(templates)
        
        # 准备实体数据
        entities = {
            'person': self.generate_person_name(),
            'location': random.choice(self.locations),
            'organization': random.choice(self.organizations),
            'time': self.generate_time(),
            'money': self.generate_money(),
            'product': random.choice(self.products),
            'event': random.choice(self.events)
        }
        
        # 替换占位符
        for key, value in entities.items():
            template = template.replace(f'{{{key}}}', value)
        
        return template
    
    def char_level_bio_tagging(self, sentence: str) -> List[Tuple[str, str]]:
        """字符级别BIO标注"""
        chars = list(sentence)
        tags = ['O'] * len(chars)

        # 先标注完整的实体（组织、地点、产品、事件）
        complete_entities = {
            'ORG': self.organizations,
            'LOC': self.locations,
            'PRODUCT': self.products,
            'EVENT': self.events
        }

        for entity_type, entities in complete_entities.items():
            for entity in sorted(entities, key=len, reverse=True):
                start = 0
                while True:
                    pos = sentence.find(entity, start)
                    if pos == -1:
                        break

                    if all(tags[pos + i] == 'O' for i in range(len(entity))):
                        for i in range(len(entity)):
                            tags[pos + i] = f'B-{entity_type}' if i == 0 else f'I-{entity_type}'

                    start = pos + 1

        # 标注人名（需要更智能的处理）
        self._tag_person_names(sentence, chars, tags)

        # 正则标注时间和金额
        self._tag_time_and_money(sentence, tags)

        return list(zip(chars, tags))

    def _tag_person_names(self, sentence: str, chars: List[str], tags: List[str]):
        """智能标注人名"""
        # 查找姓氏+名字的组合
        for i, char in enumerate(chars):
            if tags[i] != 'O':  # 已被标注，跳过
                continue

            if char in self.surnames:
                # 找到姓氏，检查后面是否跟着名字
                name_length = self._get_person_name_length(sentence, i)
                if name_length > 1:  # 至少是姓+名的组合
                    # 检查是否为停用词
                    full_name = sentence[i:i+name_length]
                    if not any(stop_char in full_name for stop_char in self.stop_chars):
                        # 标注人名
                        for j in range(name_length):
                            if i + j < len(tags) and tags[i + j] == 'O':
                                tags[i + j] = f'B-PER' if j == 0 else f'I-PER'

    def _get_person_name_length(self, sentence: str, start_pos: int) -> int:
        """获取从指定位置开始的人名长度"""
        if start_pos >= len(sentence):
            return 0

        length = 1  # 姓氏

        # 检查后面最多3个字符是否为名字
        for i in range(1, min(4, len(sentence) - start_pos)):
            char = sentence[start_pos + i]
            if char in self.given_names:
                length += 1
            else:
                break

        # 人名至少要2个字符（姓+名）
        return length if length >= 2 else 0

    def _tag_time_and_money(self, sentence: str, tags: List[str]):
        """标注时间和金额"""
        # 时间模式
        time_patterns = [
            (r'\d{4}年\d{1,2}月\d{1,2}日', 'TIME'),
            (r'\d{1,2}月\d{1,2}日', 'TIME'),
            (r'今年\d{1,2}月', 'TIME'),
            (r'\d{4}年', 'TIME')
        ]

        # 金额模式
        money_patterns = [
            (r'\d+万元', 'MONEY'),
            (r'\d+亿元', 'MONEY'),
            (r'\d+元', 'MONEY')
        ]

        # 标注时间和金额
        for patterns_list in [time_patterns, money_patterns]:
            for pattern, entity_type in patterns_list:
                for match in re.finditer(pattern, sentence):
                    start, end = match.span()
                    if all(tags[i] == 'O' for i in range(start, end)):
                        for i in range(start, end):
                            tags[i] = f'B-{entity_type}' if i == start else f'I-{entity_type}'
    
    def generate_dataset(self, num_samples: int = None) -> List[Dict]:
        """生成数据集"""
        if num_samples is None:
            num_samples = self.config['num_samples']
        
        dataset = []
        complex_ratio = self.config.get('complex_sentence_ratio', 0.7)
        
        print(f"开始生成{num_samples}条NER数据...")
        
        for i in range(num_samples):
            if i % 1000 == 0:
                print(f"已生成 {i}/{num_samples} 条数据")
            
            complexity = 'complex' if random.random() < complex_ratio else 'simple'
            sentence = self.generate_sentence(complexity)
            char_tags = self.char_level_bio_tagging(sentence)
            
            sample = {
                'id': i + 1,
                'sentence': sentence,
                'chars': [char for char, _ in char_tags],
                'tags': [tag for _, tag in char_tags],
                'length': len(sentence),
                'complexity': complexity
            }
            
            dataset.append(sample)
        
        print(f"数据生成完成！共生成{len(dataset)}条数据")
        return dataset
    
    def save_dataset(self, dataset: List[Dict], output_dir: str = "data", formats: List[str] = None):
        """保存数据集"""
        if formats is None:
            formats = self.config.get('output_formats', ['json'])
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        for format_type in formats:
            if format_type == 'json':
                filename = f"{output_dir}/ner_dataset_{timestamp}.json"
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(dataset, f, ensure_ascii=False, indent=2)
                print(f"JSON格式数据集已保存到: {filename}")
                
            elif format_type == 'conll':
                filename = f"{output_dir}/ner_dataset_{timestamp}.conll"
                with open(filename, 'w', encoding='utf-8') as f:
                    for sample in dataset:
                        f.write(f"# Sentence {sample['id']}: {sample['sentence']}\n")
                        for char, tag in zip(sample['chars'], sample['tags']):
                            f.write(f"{char}\t{tag}\n")
                        f.write("\n")
                print(f"CoNLL格式数据集已保存到: {filename}")
                
            elif format_type == 'bio':
                filename = f"{output_dir}/ner_dataset_{timestamp}.bio"
                with open(filename, 'w', encoding='utf-8') as f:
                    for sample in dataset:
                        chars_line = " ".join(sample['chars'])
                        tags_line = " ".join(sample['tags'])
                        f.write(f"{chars_line}\n{tags_line}\n\n")
                print(f"BIO格式数据集已保存到: {filename}")
    
    def analyze_dataset(self, dataset: List[Dict]):
        """分析数据集统计信息"""
        total_samples = len(dataset)
        total_chars = sum(sample['length'] for sample in dataset)
        avg_length = total_chars / total_samples
        
        # 统计实体类型
        entity_counts = {}
        for sample in dataset:
            for tag in sample['tags']:
                if tag != 'O':
                    entity_type = tag.split('-')[1]
                    entity_counts[entity_type] = entity_counts.get(entity_type, 0) + 1
        
        print("\n=== 数据集统计信息 ===")
        print(f"总样本数: {total_samples}")
        print(f"总字符数: {total_chars}")
        print(f"平均句子长度: {avg_length:.2f} 字符")
        print("实体类型分布:")
        for entity_type, count in sorted(entity_counts.items()):
            print(f"  {entity_type}: {count} 个")
