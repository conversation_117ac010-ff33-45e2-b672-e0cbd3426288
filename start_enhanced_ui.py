#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动增强版NER标注UI
集成了智能预标注功能
"""

import sys
import os

# 确保正确的路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

try:
    from ner_annotation_ui_with_model import main
    
    if __name__ == "__main__":
        print("🚀 启动增强版NER标注UI...")
        print("✨ 功能包括:")
        print("   📝 数据浏览和标注")
        print("   🔍 可疑数据检查") 
        print("   ✏️ 手动标注编辑")
        print("   🧠 AI智能预标注")
        print("=" * 50)
        
        main()
        
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保所有依赖包已安装")
except Exception as e:
    print(f"❌ 启动失败: {e}")