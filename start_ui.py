#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NER数据标注工具启动脚本
"""

import sys
import os

def main():
    """启动NER标注工具"""
    print("启动NER数据标注工具...")
    print("=" * 50)
    
    try:
        # 检查依赖
        import tkinter as tk
        print("✓ Tkinter 可用")
        
        # 启动UI
        from ner_annotation_ui import NERAnnotationUI
        
        root = tk.Tk()
        app = NERAnnotationUI(root)
        
        print("✓ UI界面已启动")
        print("\n使用说明:")
        print("1. 点击'加载数据集'按钮加载JSON格式的NER数据")
        print("2. 使用'数据浏览'标签页查看数据")
        print("3. 使用'可疑数据检查'标签页检测问题")
        print("4. 使用'手动标注'标签页修正错误")
        print("5. 使用'统计信息'标签页查看数据统计")
        print("\n快捷键:")
        print("- Ctrl+O: 打开文件")
        print("- Ctrl+S: 保存文件")
        print("- 左箭头: 上一个样本")
        print("- 右箭头: 下一个样本")
        
        root.mainloop()
        
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请确保已安装所需的Python包")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
