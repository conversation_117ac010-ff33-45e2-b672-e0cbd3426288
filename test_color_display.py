#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试标注颜色显示功能
验证UI中的实体高亮是否正常工作
"""

import json
import subprocess
import sys

def verify_test_data():
    """验证测试数据的正确性"""
    print("🔍 验证测试数据...")
    
    with open("test_annotated_data.json", 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    for i, sample in enumerate(data):
        text = sample['text']
        labels = sample['labels']
        
        print(f"\n样本 {i+1}: {text}")
        print(f"文本长度: {len(text)}, 标签长度: {len(labels)}")
        
        if len(text) != len(labels):
            print(f"❌ 警告: 样本{i+1}的文本与标签长度不匹配!")
            return False
        
        # 统计实体
        entities = []
        current_entity = ""
        current_type = ""
        
        for j, (char, label) in enumerate(zip(text, labels)):
            if label.startswith('B-'):
                if current_entity:
                    entities.append((current_entity, current_type))
                current_entity = char
                current_type = label[2:]
            elif label.startswith('I-'):
                if current_type == label[2:]:
                    current_entity += char
                else:
                    if current_entity:
                        entities.append((current_entity, current_type))
                    current_entity = char
                    current_type = label[2:]
            else:
                if current_entity:
                    entities.append((current_entity, current_type))
                    current_entity = ""
                    current_type = ""
        
        if current_entity:
            entities.append((current_entity, current_type))
        
        print(f"实体: {entities}")
    
    print(f"\n✅ 验证完成! 共 {len(data)} 个样本，数据格式正确")
    return True

def print_usage_guide():
    """打印使用指南"""
    print("""
🎨 颜色显示测试指南
=================================

📋 测试步骤:
1. 启动UI: python start_enhanced_ui.py
2. 加载测试数据: test_annotated_data.json
3. 在"数据浏览"页面查看样本
4. 检查"标注预览"区域的颜色高亮

🎨 颜色映射:
• PER (人名): 🌸 浅粉色
• ORG (组织): 🌌 天蓝色  
• LOC (地点): 🌿 浅绿色
• TIME (时间): 🌺 梅花色
• MONEY (金钱): 🌻 卡其色
• PRODUCT (产品): 🍑 浅鲑鱼色
• EVENT (事件): 🌊 浅海绿色

🔧 调试功能:
• 勾选工具栏的"🔧 调试模式"
• 查看详细的实体统计信息
• 检查文本和标签长度是否匹配

❓ 如果没有颜色显示:
1. 检查数据格式是否正确
2. 启用调试模式查看详细信息
3. 确认标签使用BIO格式 (B-TYPE, I-TYPE, O)
4. 重新加载数据或重启UI

✅ 预期效果:
• 人名应显示为浅粉色背景
• 组织机构显示为天蓝色背景
• 地点显示为浅绿色背景
• 时间显示为梅花色背景
• 金钱显示为卡其色背景
• 产品显示为浅鲑鱼色背景
• 事件显示为浅海绿色背景
""")

def main():
    """主函数"""
    print("🎨 标注颜色显示测试")
    print("=" * 50)
    
    # 验证测试数据
    if not verify_test_data():
        print("❌ 测试数据验证失败")
        return
    
    # 打印使用指南
    print_usage_guide()
    
    # 询问是否启动UI
    response = input("是否立即启动UI进行颜色显示测试？(y/n): ").strip().lower()
    
    if response in ['y', 'yes', '是']:
        print("\n🚀 启动增强版UI...")
        try:
            subprocess.run([sys.executable, "start_enhanced_ui.py"])
        except Exception as e:
            print(f"❌ 启动UI失败: {e}")
            print("💡 请手动运行: python start_enhanced_ui.py")
            print("💡 然后加载文件: test_annotated_data.json")
    else:
        print("\n💡 手动测试步骤:")
        print("1. 运行: python start_enhanced_ui.py")
        print("2. 加载文件: test_annotated_data.json")
        print("3. 查看标注预览区域的颜色高亮")

if __name__ == "__main__":
    main()