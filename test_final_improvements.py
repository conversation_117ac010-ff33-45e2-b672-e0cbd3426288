#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终改进效果
验证修复后的UI功能是否正常工作
"""

import subprocess
import sys
import json
import os

def test_color_display():
    """测试颜色显示修复"""
    print("🎨 测试颜色显示修复...")
    
    if not os.path.exists("test_annotated_data.json"):
        print("❌ 测试数据文件不存在")
        return False
    
    with open("test_annotated_data.json", 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    all_valid = True
    for i, sample in enumerate(data):
        text = sample['text']
        labels = sample['labels']
        
        if len(text) != len(labels):
            print(f"❌ 样本{i+1}: 文本长度({len(text)}) != 标签长度({len(labels)})")
            all_valid = False
    
    if all_valid:
        print("✅ 所有测试数据格式正确，颜色显示问题已修复")
    
    return all_valid

def test_improved_inference():
    """测试改进的推理功能"""
    print("\n🚀 测试改进的推理功能...")
    
    try:
        # 导入测试
        from improve_annotation_quality import ImprovedNERInference
        print("✅ 改进推理模块导入成功")
        
        # 检查模型文件
        if os.path.exists("model/best_model"):
            print("✅ 找到best_model目录")
            return True
        elif os.path.exists("model/chinese_ner_model"):
            print("✅ 找到chinese_ner_model目录")
            return True
        else:
            print("❌ 未找到模型文件")
            return False
            
    except ImportError as e:
        print(f"❌ 改进推理模块导入失败: {e}")
        return False

def test_ui_integration():
    """测试UI集成"""
    print("\n🖥️ 测试UI集成...")
    
    try:
        # 检查UI文件
        if os.path.exists("ner_annotation_ui_with_model.py"):
            print("✅ 增强版UI文件存在")
            
            # 检查关键代码
            with open("ner_annotation_ui_with_model.py", 'r', encoding='utf-8') as f:
                content = f.read()
                
            checks = [
                ("ImprovedNERInference", "改进推理类导入"),
                ("use_improved_model", "改进模型开关"),
                ("toggle_model_mode", "模型切换功能"),
                ("improvements_applied", "改进标记")
            ]
            
            all_checks_passed = True
            for check_str, description in checks:
                if check_str in content:
                    print(f"✅ {description}: 已集成")
                else:
                    print(f"❌ {description}: 未找到")
                    all_checks_passed = False
            
            return all_checks_passed
        else:
            print("❌ UI文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ UI集成测试失败: {e}")
        return False

def check_files():
    """检查关键文件"""
    print("\n📁 检查关键文件...")
    
    files = [
        ("ner_annotation_ui_with_model.py", "增强版UI"),
        ("improve_annotation_quality.py", "改进推理模块"),
        ("test_annotated_data.json", "测试标注数据"),
        ("diagnose_model_performance.py", "诊断工具"),
        ("智能标注改进总结.md", "改进总结文档"),
        ("start_enhanced_ui.py", "UI启动脚本")
    ]
    
    all_exist = True
    for filename, description in files:
        if os.path.exists(filename):
            file_size = os.path.getsize(filename)
            print(f"✅ {description}: {filename} ({file_size} bytes)")
        else:
            print(f"❌ {description}: {filename} - 文件不存在")
            all_exist = False
    
    return all_exist

def create_quick_test_data():
    """创建快速测试数据"""
    quick_test_data = [
        {
            "text": "张三在北京工作",
            "labels": ["B-PER", "I-PER", "O", "B-LOC", "I-LOC", "O", "O"]
        },
        {
            "text": "苹果公司发布iPhone",
            "labels": ["B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "B-PRODUCT", "I-PRODUCT", "I-PRODUCT", "I-PRODUCT", "I-PRODUCT", "I-PRODUCT"]
        }
    ]
    
    with open("quick_test_data.json", 'w', encoding='utf-8') as f:
        json.dump(quick_test_data, f, ensure_ascii=False, indent=2)
    
    print("\n📝 创建快速测试数据: quick_test_data.json")
    return "quick_test_data.json"

def print_usage_instructions():
    """打印使用说明"""
    print("""
🎯 智能标注改进完成！使用指南：
=====================================

🚀 启动改进版UI:
   python start_enhanced_ui.py

📊 测试改进效果:
   1. 加载测试数据: test_annotated_data.json 或 quick_test_data.json
   2. 切换到"智能标注"页面
   3. 点击"🧠 对当前样本进行智能预标注"
   4. 观察改进效果和状态信息

⚡ 新功能特性:
   • 🚀 改进模型 - 提供更准确的实体识别
   • ⚡ 使用改进模型 - 工具栏切换开关
   • 🔧 调试模式 - 显示详细预测信息
   • 📊 批量标注 - 支持批量智能预标注
   • 🎨 颜色显示 - 修复了标注颜色显示问题

🔧 故障排除:
   • 如果改进模型加载失败，会自动降级到基础模型
   • 如果颜色不显示，检查数据格式是否正确
   • 如果预测效果不理想，可以切换到基础模型对比

📈 改进效果:
   • 提高了实体识别准确性
   • 减少了类型分类错误
   • 改善了实体边界定位
   • 过滤了低质量预测

💡 最佳实践:
   • 默认使用改进模型获得更好效果
   • 启用调试模式查看详细信息
   • 对重要数据进行人工验证
   • 根据领域特点调整置信度阈值
""")

def main():
    """主函数"""
    print("🧪 智能标注改进效果测试")
    print("=" * 50)
    
    # 测试各个组件
    results = []
    
    results.append(("文件检查", check_files()))
    results.append(("颜色显示修复", test_color_display()))
    results.append(("改进推理功能", test_improved_inference()))
    results.append(("UI集成", test_ui_integration()))
    
    # 创建快速测试数据
    create_quick_test_data()
    
    # 总结测试结果
    print(f"\n📊 测试结果总结:")
    print("=" * 30)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print(f"\n🎉 所有测试通过！智能标注改进已成功完成。")
        print_usage_instructions()
    else:
        print(f"\n⚠️ 部分测试失败，请检查相关组件。")
    
    # 询问是否启动UI测试
    try:
        response = input("\n是否立即启动UI进行实际测试？(y/n): ").strip().lower()
        if response in ['y', 'yes', '是']:
            print("\n🚀 启动增强版UI...")
            subprocess.run([sys.executable, "start_enhanced_ui.py"])
    except KeyboardInterrupt:
        print("\n👋 测试结束")

if __name__ == "__main__":
    main()