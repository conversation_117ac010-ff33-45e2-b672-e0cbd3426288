#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的标注逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from src.ner_generator import NERGenerator

def test_tagging():
    """测试标注逻辑"""
    generator = NERGenerator()
    
    # 测试问题句子
    test_sentences = [
        "用于小米手机的研发",
        "张三在北京大学工作",
        "李四于2024年参加会议",
        "投资100万元用于产品开发"
    ]
    
    print("=== 测试标注修复 ===")
    for sentence in test_sentences:
        print(f"\n句子: {sentence}")
        char_tags = generator.char_level_bio_tagging(sentence)
        
        print("标注结果:")
        for char, tag in char_tags:
            if tag != 'O':
                print(f"  {char}: {tag}")
            else:
                print(f"  {char}: O")

if __name__ == "__main__":
    test_tagging()
