#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试NER数据生成器
"""

from enhanced_ner_generator import EnhancedNERGenerator

def test_basic_generation():
    """测试基本生成功能"""
    print("=== 测试基本生成功能 ===")
    
    # 创建生成器
    config = {
        'num_samples': 10,
        'complex_sentence_ratio': 0.5,
        'random_seed': 42
    }
    
    generator = EnhancedNERGenerator(config)
    
    # 测试句子生成
    print("生成的句子示例:")
    for i in range(5):
        simple_sentence = generator.generate_sentence('simple')
        complex_sentence = generator.generate_sentence('complex')
        print(f"简单句子 {i+1}: {simple_sentence}")
        print(f"复杂句子 {i+1}: {complex_sentence}")
        print()
    
    # 测试BIO标注
    test_sentence = "张三在北京大学工作，月薪5000元"
    char_tags = generator.char_level_bio_tagging(test_sentence)
    
    print("BIO标注测试:")
    print(f"句子: {test_sentence}")
    print("字符-标签对:")
    for char, tag in char_tags:
        print(f"  {char}: {tag}")
    
    # 生成小数据集
    print("\n生成小数据集...")
    dataset = generator.generate_dataset()
    
    # 显示前3个样本
    print("\n前3个样本:")
    for i in range(3):
        sample = dataset[i]
        print(f"\n样本 {sample['id']}:")
        print(f"句子: {sample['sentence']}")
        print(f"长度: {sample['length']}")
        print(f"复杂度: {sample['complexity']}")
        
        # 提取实体
        entities = []
        current_entity = ""
        current_type = ""
        
        for char, tag in zip(sample['chars'], sample['tags']):
            if tag.startswith('B-'):
                if current_entity:
                    entities.append((current_entity, current_type))
                current_entity = char
                current_type = tag[2:]
            elif tag.startswith('I-') and current_entity:
                current_entity += char
            else:
                if current_entity:
                    entities.append((current_entity, current_type))
                    current_entity = ""
                    current_type = ""
        
        if current_entity:
            entities.append((current_entity, current_type))
        
        print("实体:")
        for entity, entity_type in entities:
            print(f"  {entity} -> {entity_type}")

if __name__ == "__main__":
    test_basic_generation()
