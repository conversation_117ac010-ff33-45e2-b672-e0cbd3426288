#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建用于测试图例效果的数据
"""

import json

def create_legend_test_data():
    """创建包含所有实体类型的测试数据"""
    test_data = [
        {
            "id": 1,
            "sentence": "张三在北京大学工作，月薪5000元",
            "chars": ["张", "三", "在", "北", "京", "大", "学", "工", "作", "，", "月", "薪", "5", "0", "0", "0", "元"],
            "tags": ["B-PER", "I-PER", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "B-MONEY", "I-MONEY", "I-MONEY", "I-MONEY", "I-MONEY"],
            "length": 17,
            "complexity": "simple"
        },
        {
            "id": 2,
            "sentence": "李四于2024年1月在上海市参加春节联欢晚会",
            "chars": ["李", "四", "于", "2", "0", "2", "4", "年", "1", "月", "在", "上", "海", "市", "参", "加", "春", "节", "联", "欢", "晚", "会"],
            "tags": ["B-PER", "I-PER", "O", "B-TIME", "I-TIME", "I-TIME", "I-TIME", "I-TIME", "I-TIME", "I-TIME", "O", "B-LOC", "I-LOC", "I-LOC", "O", "O", "B-EVENT", "I-EVENT", "I-EVENT", "I-EVENT", "I-EVENT", "I-EVENT"],
            "length": 22,
            "complexity": "complex"
        },
        {
            "id": 3,
            "sentence": "王五购买了iPhone 15手机，花费8000元",
            "chars": ["王", "五", "购", "买", "了", "i", "P", "h", "o", "n", "e", " ", "1", "5", "手", "机", "，", "花", "费", "8", "0", "0", "0", "元"],
            "tags": ["B-PER", "I-PER", "O", "O", "O", "B-PRODUCT", "I-PRODUCT", "I-PRODUCT", "I-PRODUCT", "I-PRODUCT", "I-PRODUCT", "I-PRODUCT", "I-PRODUCT", "I-PRODUCT", "O", "O", "O", "O", "O", "B-MONEY", "I-MONEY", "I-MONEY", "I-MONEY", "I-MONEY"],
            "length": 24,
            "complexity": "simple"
        },
        {
            "id": 4,
            "sentence": "赵六在腾讯科技公司开发微信产品",
            "chars": ["赵", "六", "在", "腾", "讯", "科", "技", "公", "司", "开", "发", "微", "信", "产", "品"],
            "tags": ["B-PER", "I-PER", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "B-PRODUCT", "I-PRODUCT", "O", "O"],
            "length": 15,
            "complexity": "simple"
        },
        {
            "id": 5,
            "sentence": "孙七计划在明年3月前往深圳市参加科技博览会，预算100万元",
            "chars": ["孙", "七", "计", "划", "在", "明", "年", "3", "月", "前", "往", "深", "圳", "市", "参", "加", "科", "技", "博", "览", "会", "，", "预", "算", "1", "0", "0", "万", "元"],
            "tags": ["B-PER", "I-PER", "O", "O", "O", "B-TIME", "I-TIME", "I-TIME", "I-TIME", "O", "O", "B-LOC", "I-LOC", "I-LOC", "O", "O", "B-EVENT", "I-EVENT", "I-EVENT", "I-EVENT", "I-EVENT", "O", "O", "O", "B-MONEY", "I-MONEY", "I-MONEY", "I-MONEY", "I-MONEY"],
            "length": 29,
            "complexity": "complex"
        }
    ]
    
    # 保存测试数据
    with open("data/legend_test.json", "w", encoding="utf-8") as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    print("图例测试数据已创建: data/legend_test.json")
    print("\n数据包含所有7种实体类型:")
    print("🏷️ PER (人名): 张三、李四、王五、赵六、孙七")
    print("🏢 ORG (组织): 北京大学、腾讯科技公司")
    print("📍 LOC (地点): 上海市、深圳市")
    print("⏰ TIME (时间): 2024年1月、明年3月")
    print("💰 MONEY (金额): 5000元、8000元、100万元")
    print("📱 PRODUCT (产品): iPhone 15、微信")
    print("🎯 EVENT (事件): 春节联欢晚会、科技博览会")
    
    print("\n测试步骤:")
    print("1. 启动UI: python ner_annotation_ui.py")
    print("2. 加载数据: data/legend_test.json")
    print("3. 观察图例显示效果")
    print("4. 浏览不同样本，查看彩色标注效果")
    print("5. 验证图例颜色与实体标注颜色是否一致")
    
    return test_data

if __name__ == "__main__":
    create_legend_test_data()
