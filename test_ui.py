#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI功能的脚本
"""

import json
import os

def create_test_data():
    """创建测试数据"""
    test_data = [
        {
            "id": 1,
            "sentence": "张三在北京大学工作",
            "chars": ["张", "三", "在", "北", "京", "大", "学", "工", "作"],
            "tags": ["B-PER", "I-PER", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O"],
            "length": 9,
            "complexity": "simple"
        },
        {
            "id": 2,
            "sentence": "李四于2024年参加了世界杯",
            "chars": ["李", "四", "于", "2", "0", "2", "4", "年", "参", "加", "了", "世", "界", "杯"],
            "tags": ["B-PER", "I-PER", "O", "B-TIME", "I-TIME", "I-TIME", "I-TIME", "I-TIME", "O", "O", "O", "B-EVENT", "I-EVENT", "I-EVENT"],
            "length": 14,
            "complexity": "simple"
        },
        {
            "id": 3,
            "sentence": "阿里巴巴集团投资100万元用于产品研发",
            "chars": ["阿", "里", "巴", "巴", "集", "团", "投", "资", "1", "0", "0", "万", "元", "用", "于", "产", "品", "研", "发"],
            "tags": ["B-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "B-MONEY", "I-MONEY", "I-MONEY", "I-MONEY", "I-MONEY", "O", "O", "O", "O", "O", "O"],
            "length": 19,
            "complexity": "complex"
        },
        {
            "id": 4,
            "sentence": "于在上海市的华为公司工作",  # 故意的错误示例
            "chars": ["于", "在", "上", "海", "市", "的", "华", "为", "公", "司", "工", "作"],
            "tags": ["B-PER", "O", "B-LOC", "I-LOC", "I-LOC", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O"],
            "length": 12,
            "complexity": "simple"
        },
        {
            "id": 5,
            "sentence": "王五购买了iPhone 15手机",
            "chars": ["王", "五", "购", "买", "了", "i", "P", "h", "o", "n", "e", " ", "1", "5", "手", "机"],
            "tags": ["B-PER", "I-PER", "O", "O", "O", "B-PRODUCT", "I-PRODUCT", "I-PRODUCT", "I-PRODUCT", "I-PRODUCT", "I-PRODUCT", "I-PRODUCT", "I-PRODUCT", "I-PRODUCT", "O", "O"],
            "length": 16,
            "complexity": "simple"
        }
    ]
    
    # 保存测试数据
    with open("data/test_dataset.json", "w", encoding="utf-8") as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    print("测试数据已创建: data/test_dataset.json")
    return test_data

def test_ui_functions():
    """测试UI功能"""
    print("NER标注工具UI测试")
    print("=" * 50)
    
    # 创建测试数据
    test_data = create_test_data()
    
    print(f"✓ 创建了 {len(test_data)} 条测试数据")
    print("\n测试数据包含:")
    print("- 正常的人名、组织、地点标注")
    print("- 时间和金额实体")
    print("- 产品名称标注")
    print("- 故意的错误标注（样本4中的'于'被错误标注为人名）")
    
    print("\n建议测试流程:")
    print("1. 启动UI: python ner_annotation_ui.py")
    print("2. 加载测试数据: data/test_dataset.json")
    print("3. 在'数据浏览'中查看彩色标注效果")
    print("4. 在'可疑数据检查'中检测问题（应该发现样本4的问题）")
    print("5. 在'手动标注'中修正错误")
    print("6. 在'统计信息'中查看数据统计")
    print("7. 保存修改后的数据")
    
    print("\n预期结果:")
    print("- 样本4中的'于'应该被检测为可疑人名")
    print("- 可以通过手动标注将'于'的标签改为'O'")
    print("- 统计信息应该显示5个样本的详细统计")

def main():
    """主函数"""
    # 确保data目录存在
    os.makedirs("data", exist_ok=True)
    
    # 运行测试
    test_ui_functions()
    
    print("\n" + "=" * 50)
    print("测试准备完成！现在可以启动UI进行测试。")

if __name__ == "__main__":
    main()
