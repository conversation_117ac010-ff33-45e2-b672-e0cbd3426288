#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI修复效果的脚本
"""

import json

def create_simple_test_data():
    """创建简单的测试数据"""
    test_data = [
        {
            "id": 1,
            "sentence": "张三在北京工作",
            "chars": ["张", "三", "在", "北", "京", "工", "作"],
            "tags": ["B-PER", "I-PER", "O", "B-LOC", "I-LOC", "O", "O"],
            "length": 7,
            "complexity": "simple"
        },
        {
            "id": 2,
            "sentence": "李四买了苹果手机",
            "chars": ["李", "四", "买", "了", "苹", "果", "手", "机"],
            "tags": ["B-PER", "I-PER", "O", "O", "B-PRODUCT", "I-PRODUCT", "I-PRODUCT", "I-PRODUCT"],
            "length": 8,
            "complexity": "simple"
        },
        {
            "id": 3,
            "sentence": "王五在2024年参加比赛",
            "chars": ["王", "五", "在", "2", "0", "2", "4", "年", "参", "加", "比", "赛"],
            "tags": ["B-PER", "I-PER", "O", "B-TIME", "I-TIME", "I-TIME", "I-TIME", "I-TIME", "O", "O", "O", "O"],
            "length": 12,
            "complexity": "simple"
        }
    ]
    
    # 保存测试数据
    with open("data/simple_test.json", "w", encoding="utf-8") as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    print("简单测试数据已创建: data/simple_test.json")
    print("\n测试步骤:")
    print("1. 启动UI: python ner_annotation_ui.py")
    print("2. 加载数据: data/simple_test.json")
    print("3. 点击'下一条'按钮，观察句子显示框是否正确更新")
    print("4. 检查彩色标注是否正确显示")
    
    print("\n预期结果:")
    print("- 样本1: 张三(粉色) 在 北京(绿色) 工作")
    print("- 样本2: 李四(粉色) 买了 苹果手机(橙色)")
    print("- 样本3: 王五(粉色) 在 2024年(紫色) 参加比赛")
    
    return test_data

if __name__ == "__main__":
    create_simple_test_data()
