
# 在 ner_annotation_ui_with_model.py 中的改进集成代码

def init_improved_model(self):
    """初始化改进的NER模型"""
    if not MODEL_AVAILABLE:
        return
        
    try:
        # 使用改进的推理器
        from improve_annotation_quality import ImprovedNERInference
        
        best_model_path = os.path.join(os.path.dirname(__file__), 'model', 'best_model')
        chinese_model_path = os.path.join(os.path.dirname(__file__), 'model', 'chinese_ner_model')
        
        model_path = None
        if os.path.exists(best_model_path):
            model_path = best_model_path
        elif os.path.exists(chinese_model_path):
            model_path = chinese_model_path
        
        if model_path:
            self.improved_inference = ImprovedNERInference(model_path)
            self.model_loaded = True
            print(f"✅ 改进模型加载成功: {model_path}")
        else:
            print("⚠️  未找到训练好的模型文件")
            
    except Exception as e:
        print(f"❌ 改进模型加载失败: {e}")
        self.model_loaded = False

def predict_entities_improved(self, text: str):
    """使用改进的模型预测实体"""
    if not self.model_loaded or not hasattr(self, 'improved_inference'):
        return None
        
    try:
        # 使用改进的推理器
        result = self.improved_inference.predict_with_improvements(text)
        return result
        
    except Exception as e:
        print(f"改进预测失败: {e}")
        return None
