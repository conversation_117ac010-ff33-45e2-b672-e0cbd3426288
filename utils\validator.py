#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NER数据验证工具
"""

import json
from typing import List, Dict
from collections import Counter

class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def load_dataset(filename: str) -> List[Dict]:
        """加载JSON格式数据集"""
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    @staticmethod
    def validate_bio_format(dataset: List[Dict]) -> bool:
        """验证BIO格式正确性"""
        print("=== BIO格式验证 ===")
        errors = []
        
        for sample in dataset:
            tags = sample['tags']
            chars = sample['chars']
            
            for i, tag in enumerate(tags):
                if tag.startswith('I-'):
                    if i == 0:
                        errors.append(f"样本{sample['id']}: I标签不能出现在句子开头")
                    else:
                        prev_tag = tags[i-1]
                        entity_type = tag[2:]
                        expected = [f"B-{entity_type}", f"I-{entity_type}"]
                        if prev_tag not in expected:
                            errors.append(f"样本{sample['id']}: I标签前应为B或同类型I标签")
        
        if errors:
            print(f"发现{len(errors)}个错误")
            for error in errors[:5]:
                print(f"  {error}")
            return False
        else:
            print("✓ BIO格式验证通过")
            return True
    
    @staticmethod
    def analyze_statistics(dataset: List[Dict]):
        """分析数据统计信息"""
        print("\n=== 数据统计分析 ===")
        
        total_samples = len(dataset)
        lengths = [sample['length'] for sample in dataset]
        
        # 基本统计
        print(f"总样本数: {total_samples}")
        print(f"平均长度: {sum(lengths) / len(lengths):.2f} 字符")
        print(f"长度范围: {min(lengths)}-{max(lengths)} 字符")
        
        # 实体统计
        entity_counts = Counter()
        for sample in dataset:
            for tag in sample['tags']:
                if tag != 'O':
                    entity_type = tag.split('-')[1]
                    entity_counts[entity_type] += 1
        
        print("\n实体类型分布:")
        for entity_type, count in entity_counts.most_common():
            print(f"  {entity_type}: {count} 个")
        
        # 复杂度统计
        if 'complexity' in dataset[0]:
            complexity_counts = Counter(sample['complexity'] for sample in dataset)
            print("\n复杂度分布:")
            for complexity, count in complexity_counts.items():
                percentage = count / total_samples * 100
                print(f"  {complexity}: {count} 条 ({percentage:.1f}%)")
    
    @staticmethod
    def check_diversity(dataset: List[Dict]):
        """检查数据多样性"""
        print("\n=== 多样性检查 ===")
        
        sentences = [sample['sentence'] for sample in dataset]
        unique_sentences = set(sentences)
        
        duplicate_rate = (len(sentences) - len(unique_sentences)) / len(sentences) * 100
        print(f"重复率: {duplicate_rate:.2f}%")
        
        if duplicate_rate > 5:
            print("⚠️ 重复率较高，建议增加数据多样性")
        else:
            print("✓ 数据多样性良好")
    
    @classmethod
    def validate_dataset(cls, filename: str):
        """完整验证数据集"""
        print(f"验证数据集: {filename}")
        print("=" * 50)
        
        dataset = cls.load_dataset(filename)
        
        cls.validate_bio_format(dataset)
        cls.analyze_statistics(dataset)
        cls.check_diversity(dataset)
        
        print("\n" + "=" * 50)
        print("验证完成！")
