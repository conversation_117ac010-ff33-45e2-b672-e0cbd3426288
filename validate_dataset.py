#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证生成的NER数据集质量
"""

import json
import re
from collections import Counter

def load_dataset(filename):
    """加载JSON格式的数据集"""
    with open(filename, 'r', encoding='utf-8') as f:
        return json.load(f)

def validate_bio_format(dataset):
    """验证BIO格式的正确性"""
    print("=== BIO格式验证 ===")
    errors = []
    
    for sample in dataset:
        tags = sample['tags']
        chars = sample['chars']
        
        for i, tag in enumerate(tags):
            if tag.startswith('I-'):
                # I标签前面应该是B标签或同类型的I标签
                if i == 0:
                    errors.append(f"样本{sample['id']}: 第{i+1}个字符'{chars[i]}'的标签'{tag}'不能出现在句子开头")
                else:
                    prev_tag = tags[i-1]
                    entity_type = tag[2:]
                    expected_types = [f"B-{entity_type}", f"I-{entity_type}"]
                    if prev_tag not in expected_types:
                        errors.append(f"样本{sample['id']}: 第{i+1}个字符'{chars[i]}'的标签'{tag}'前面应该是{expected_types}之一，但实际是'{prev_tag}'")
    
    if errors:
        print(f"发现{len(errors)}个BIO格式错误:")
        for error in errors[:10]:  # 只显示前10个错误
            print(f"  {error}")
        if len(errors) > 10:
            print(f"  ... 还有{len(errors)-10}个错误")
        return False
    else:
        print("✓ BIO格式验证通过！")
        return True

def analyze_entity_distribution(dataset):
    """分析实体分布"""
    print("\n=== 实体分布分析 ===")
    
    entity_counts = Counter()
    entity_lengths = []
    
    for sample in dataset:
        chars = sample['chars']
        tags = sample['tags']
        
        current_entity = ""
        current_type = ""
        
        for char, tag in zip(chars, tags):
            if tag.startswith('B-'):
                if current_entity:
                    entity_counts[current_type] += 1
                    entity_lengths.append(len(current_entity))
                current_entity = char
                current_type = tag[2:]
            elif tag.startswith('I-') and current_entity:
                current_entity += char
            else:
                if current_entity:
                    entity_counts[current_type] += 1
                    entity_lengths.append(len(current_entity))
                    current_entity = ""
                    current_type = ""
        
        if current_entity:
            entity_counts[current_type] += 1
            entity_lengths.append(len(current_entity))
    
    print("实体类型分布:")
    total_entities = sum(entity_counts.values())
    for entity_type, count in entity_counts.most_common():
        percentage = count / total_entities * 100
        print(f"  {entity_type}: {count} 个 ({percentage:.1f}%)")
    
    if entity_lengths:
        avg_length = sum(entity_lengths) / len(entity_lengths)
        print(f"\n实体平均长度: {avg_length:.2f} 字符")
        print(f"最短实体长度: {min(entity_lengths)} 字符")
        print(f"最长实体长度: {max(entity_lengths)} 字符")

def analyze_sentence_quality(dataset):
    """分析句子质量"""
    print("\n=== 句子质量分析 ===")
    
    lengths = [sample['length'] for sample in dataset]
    complexities = Counter(sample['complexity'] for sample in dataset)
    
    print(f"句子数量: {len(dataset)}")
    print(f"平均长度: {sum(lengths) / len(lengths):.2f} 字符")
    print(f"最短句子: {min(lengths)} 字符")
    print(f"最长句子: {max(lengths)} 字符")
    
    print("\n复杂度分布:")
    for complexity, count in complexities.items():
        percentage = count / len(dataset) * 100
        print(f"  {complexity}: {count} 条 ({percentage:.1f}%)")

def check_data_diversity(dataset):
    """检查数据多样性"""
    print("\n=== 数据多样性检查 ===")
    
    sentences = [sample['sentence'] for sample in dataset]
    unique_sentences = set(sentences)
    
    print(f"总句子数: {len(sentences)}")
    print(f"唯一句子数: {len(unique_sentences)}")
    print(f"重复率: {(len(sentences) - len(unique_sentences)) / len(sentences) * 100:.2f}%")
    
    # 检查常见模式
    patterns = {
        '人名': r'[张李王刘陈杨赵黄周吴][a-zA-Z\u4e00-\u9fff]{1,3}',
        '时间': r'\d{4}年|\d{1,2}月\d{1,2}日|今年|明年|去年',
        '金额': r'\d+万元|\d+亿元|\d+元',
        '地点': r'[北上广深][京海州圳]|市$',
    }
    
    print("\n常见模式出现频率:")
    for pattern_name, pattern in patterns.items():
        matches = sum(1 for sentence in sentences if re.search(pattern, sentence))
        percentage = matches / len(sentences) * 100
        print(f"  {pattern_name}: {matches} 条句子 ({percentage:.1f}%)")

def sample_showcase(dataset, num_samples=5):
    """展示样本数据"""
    print(f"\n=== 随机样本展示 ({num_samples}个) ===")
    
    import random
    samples = random.sample(dataset, min(num_samples, len(dataset)))
    
    for i, sample in enumerate(samples, 1):
        print(f"\n样本 {i}:")
        print(f"ID: {sample['id']}")
        print(f"句子: {sample['sentence']}")
        print(f"长度: {sample['length']} 字符")
        print(f"复杂度: {sample['complexity']}")
        
        # 提取实体
        entities = []
        current_entity = ""
        current_type = ""
        
        for char, tag in zip(sample['chars'], sample['tags']):
            if tag.startswith('B-'):
                if current_entity:
                    entities.append((current_entity, current_type))
                current_entity = char
                current_type = tag[2:]
            elif tag.startswith('I-') and current_entity:
                current_entity += char
            else:
                if current_entity:
                    entities.append((current_entity, current_type))
                    current_entity = ""
                    current_type = ""
        
        if current_entity:
            entities.append((current_entity, current_type))
        
        print("实体:")
        for entity, entity_type in entities:
            print(f"  {entity} -> {entity_type}")

def main():
    """主函数"""
    # 查找最新的数据集文件
    import glob
    json_files = glob.glob("ner_dataset_*.json")
    if not json_files:
        print("未找到数据集文件！")
        return
    
    latest_file = max(json_files)
    print(f"验证数据集: {latest_file}")
    print("=" * 50)
    
    # 加载数据集
    dataset = load_dataset(latest_file)
    
    # 执行各种验证
    validate_bio_format(dataset)
    analyze_entity_distribution(dataset)
    analyze_sentence_quality(dataset)
    check_data_diversity(dataset)
    sample_showcase(dataset)
    
    print("\n" + "=" * 50)
    print("数据集验证完成！")

if __name__ == "__main__":
    main()
