# 🤖 智能NER标注UI使用指南

## 📋 概述

这是一个集成了AI智能预标注功能的NER（命名实体识别）数据标注工具，基于你训练好的中文NER模型，可以大幅提高标注效率。

## 🚀 启动方式

```bash
# 方法1: 使用启动脚本
python start_enhanced_ui.py

# 方法2: 直接运行UI文件
python ner_annotation_ui_with_model.py
```

## ✨ 新增功能

### 🧠 智能预标注
- **模型性能**: F1分数91.33%，人名/机构/时间/事件识别准确率100%
- **支持实体**: 人名(PER)、组织(ORG)、地点(LOC)、时间(TIME)、金钱(MONEY)、产品(PRODUCT)、事件(EVENT)
- **一键预标注**: 自动为当前样本生成实体标注
- **智能建议**: 基于训练数据的最佳预测结果

### 📊 模型状态监控
- 实时显示模型加载状态
- 模型路径自动检测
- 错误提示和解决方案

## 🎯 界面功能

### 1. 📖 数据浏览页面
- **基本功能**: 浏览、导航数据集
- **新增功能**: 
  - 🧠 智能预标注按钮
  - 🎨 实体类型颜色图例
  - 📊 实时统计信息

### 2. 🔍 可疑数据检查页面
- 数据质量检查
- 异常样本检测
- 问题报告生成

### 3. ✏️ 手动标注页面  
- **基本功能**: 手动编辑文本和标签
- **新增功能**:
  - 🧠 智能预标注 + 加载到编辑器
  - 🎨 标注参考颜色图例
  - ⚡ 一键应用AI预测结果

### 4. 🤖 智能标注页面（全新）
- **功能介绍**: AI模型性能展示
- **操作界面**: 智能预标注控制面板
- **文本预览**: 当前样本实时预览
- **使用指导**: 详细操作步骤

## 🛠️ 使用流程

### 标准标注流程
1. **加载数据** → 点击"📁 加载数据"选择JSON格式的数据集
2. **浏览样本** → 在"数据浏览"页面查看和导航样本
3. **智能预标注** → 点击"🧠 智能预标注"自动生成标注
4. **手动调整** → 在"手动标注"页面检查和修正结果
5. **保存数据** → 点击"💾 保存数据"保存标注结果

### 智能预标注操作
1. **选择样本**: 在数据浏览页面选择要标注的样本
2. **启动预标注**: 
   - 方式1: 数据浏览页面点击"🧠 智能预标注当前样本"
   - 方式2: 智能标注页面点击"🧠 对当前样本进行智能预标注"
   - 方式3: 手动标注页面点击"🧠 智能预标注"
3. **等待处理**: AI模型分析文本（通常1-3秒）
4. **查看结果**: 系统显示预测实体数量和结果
5. **手动调整**: 在手动标注页面验证和修正

## 🎨 实体类型说明

| 实体类型 | 英文标签 | 颜色 | 示例 | 格式 |
|---------|---------|------|------|------|
| 人名 | PER | 🌸 浅粉色 | 张三 | 张(B-PER) 三(I-PER) |
| 组织机构 | ORG | 🌌 天蓝色 | 清华大学 | 清(B-ORG) 华(I-ORG) 大(I-ORG) 学(I-ORG) |
| 地点位置 | LOC | 🌿 浅绿色 | 北京市 | 北(B-LOC) 京(I-LOC) 市(I-LOC) |
| 时间表达 | TIME | 🌺 梅花色 | 2023年 | 2(B-TIME) 0(I-TIME) 2(I-TIME) 3(I-TIME) 年(I-TIME) |
| 金钱数额 | MONEY | 🌻 卡其色 | 100元 | 1(B-MONEY) 0(I-MONEY) 0(I-MONEY) 元(I-MONEY) |
| 产品名称 | PRODUCT | 🍑 浅鲑鱼色 | iPhone | i(B-PRODUCT) P(I-PRODUCT) ... |
| 事件活动 | EVENT | 🌊 浅海绿色 | 春节 | 春(B-EVENT) 节(I-EVENT) |

## 🎯 标注规则

### BIO标注格式
- **B-标签**: 实体开始（Begin）
- **I-标签**: 实体内部（Inside）
- **O标签**: 非实体（Outside）

### 标注原则
1. **每个字符必须有对应标签**
2. **实体边界要准确标识**
3. **同类型实体要使用一致标签**
4. **优先使用AI预标注，然后手动调整**

## 🚨 常见问题

### 模型相关
**Q: 显示"❌ 模型未加载"怎么办？**
A: 检查以下路径是否存在模型文件：
- `./model/best_model/`
- `./model/chinese_ner_model/`

**Q: 智能预标注按钮是灰色的？**
A: 模型未正确加载，确保模型文件完整且路径正确

### 数据相关
**Q: 预标注结果不准确？**
A: 
1. AI预标注作为起点，仍需人工验证
2. 在手动标注页面调整结果
3. 对于特定领域文本，可能需要重新训练模型

**Q: 如何批量处理多个样本？**
A: 当前版本支持单个样本预标注，批量功能正在开发中

### 界面操作
**Q: 如何快速导航样本？**
A: 
- 使用导航按钮：⏮ ⏪ ⏩ ⏭
- 直接输入样本号跳转
- 在可疑数据页面快速定位问题样本

## 📊 性能对比

| 标注方式 | 效率 | 准确性 | 适用场景 |
|---------|------|--------|----------|
| 纯手工标注 | ⭐⭐ | ⭐⭐⭐⭐⭐ | 高质量要求 |
| 智能预标注+人工调整 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 推荐方式 |
| 纯AI预标注 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | 初步标注 |

## 🛠️ 技术支持

### 系统要求
- Python 3.7+
- tkinter界面库
- 已训练的NER模型文件
- 足够的内存运行模型推理

### 模型文件结构
```
model/
├── best_model/ 或 chinese_ner_model/
│   ├── config.json
│   ├── model.safetensors
│   ├── tokenizer.json
│   ├── vocab.txt
│   └── label_mapping.json
└── inference.py
```

### 数据格式要求
```json
[
  {
    "text": "张三在北京大学学习",
    "labels": ["B-PER", "I-PER", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O"]
  }
]
```

## 🔄 版本更新

### v2.0 (增强版)
- ✅ 集成AI智能预标注
- ✅ 新增智能标注专用页面  
- ✅ 模型状态实时监控
- ✅ 优化标注界面体验
- ✅ 增强错误处理机制

### v1.0 (基础版)
- ✅ 基本标注功能
- ✅ 数据质量检查
- ✅ 可疑数据检测

---

🎉 **享受智能标注带来的效率提升！** 

如有问题，请检查模型文件是否正确加载，或查看控制台错误信息。